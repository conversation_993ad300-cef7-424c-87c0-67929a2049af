<script setup lang="ts">
import { ref } from 'vue'

interface ImageFile {
  src: string
  alt: string
  file?: File
}

interface Props {
  modelValue?: ImageFile[]
  multiple?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => [],
  multiple: false
})

const emit = defineEmits<{
  'update:modelValue': [value: ImageFile[]]
}>()

const { t } = useI18n()
const {
  imageHistories,
  isLoading,
  fetchImageHistories,
  convertHistoryToImageFile
} = useImageHistory()
const fileInput = ref<HTMLInputElement | null>(null)
const isPopoverOpen = ref(false)

const handleClick = () => {
  fileInput.value?.click()
}

const handleFileChange = (event: Event) => {
  const target = event.target as HTMLInputElement
  const files = target.files

  if (files && files.length > 0) {
    const newImages: ImageFile[] = []
    const filesToProcess = props.multiple ? Array.from(files) : [files[0]]

    for (const file of filesToProcess) {
      // Only accept image files
      if (file && file.type.startsWith('image/')) {
        // Create a URL for the image
        const imageUrl = URL.createObjectURL(file)

        const imageFile: ImageFile = {
          src: imageUrl,
          alt: file.name,
          file: file
        }

        newImages.push(imageFile)
      } else {
        alert(t('imageSelect.pleaseSelectImageFile'))
        return
      }
    }

    // Emit the updated value
    if (newImages.length > 0) {
      const updatedValue = props.multiple
        ? [...props.modelValue, ...newImages]
        : newImages

      emit('update:modelValue', updatedValue)
    }

    // Reset the input so the same file can be selected again
    target.value = ''
    isPopoverOpen.value = false
  }
}

const handleImportFromHistory = () => {
  isPopoverOpen.value = true
  // Fetch images when opening popover
  if (imageHistories.value.length === 0) {
    fetchImageHistories()
  }
}

const handleSelectFromHistory = (history: any) => {
  const imageFile = convertHistoryToImageFile(history)

  const updatedValue = props.multiple
    ? [...props.modelValue, imageFile]
    : [imageFile]

  emit('update:modelValue', updatedValue)
  isPopoverOpen.value = false
}
</script>

<template>
  <div class="">
    <!-- Import from history with popover -->
    <div>
      <UPopover
        v-model:open="isPopoverOpen"
        :content="{
          align: 'center',
          side: 'top',
          sideOffset: 8
        }"
      >
        <UButton
          icon="majesticons:image-plus"
          size="sm"
          color="neutral"
          variant="outline"
          :label="multiple ? $t('addImages') : $t('addImage')"
          @click="handleImportFromHistory"
        />

        <template #content>
          <div class="p-4 w-[600px] max-h-96">
            <!-- Images grid with horizontal scroll -->
            <div class="flex gap-3 overflow-x-auto pb-2 thin-scrollbar">
              <!-- Upload from device button -->
              <div
                class="flex-shrink-0 relative group cursor-pointer rounded-lg overflow-hidden border-2 border-dashed border-gray-300 dark:border-gray-600 hover:border-primary-500 transition-colors"
                @click="handleClick"
              >
                <div class="aspect-square w-24 flex items-center justify-center bg-gray-50 dark:bg-gray-800">
                  <div class="text-center">
                    <UIcon
                      name="majesticons:image-plus"
                      class="w-6 h-6 text-gray-400 mx-auto mb-1"
                    />
                    <p class="text-xs text-gray-500">
                      {{ $t('upload') }}
                    </p>
                    <div class="text-[9px] text-gray-500">
                      .png, .jpg, .webp
                    </div>
                  </div>
                </div>

                <!-- Overlay -->
                <div
                  class="absolute inset-0 bg-primary-500/10 opacity-0 group-hover:opacity-100 transition-opacity"
                />
              </div>

              <!-- History images -->
              <div
                v-for="history in imageHistories.slice(0, 12)"
                :key="history.id"
                class="flex-shrink-0 relative group cursor-pointer rounded-lg overflow-hidden border border-gray-200 dark:border-gray-700 hover:border-primary-500 transition-colors"
                @click="handleSelectFromHistory(history)"
              >
                <div class="aspect-square w-24">
                  <img
                    :src="(history as any).thumbnail_url"
                    :alt="history.input_text"
                    class="w-full h-full object-cover"
                    loading="lazy"
                  >
                </div>

                <!-- Overlay -->
                <div
                  class="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center"
                />

                <!-- Image info -->
                <div
                  class="absolute bottom-0 left-0 right-0 p-1 bg-gradient-to-t from-black/70 to-transparent"
                >
                  <p class="text-xs text-white truncate">
                    {{ history.input_text }}
                  </p>
                </div>
              </div>

              <!-- Loading state -->
              <div
                v-if="isLoading"
                class="flex-shrink-0 aspect-square w-24 rounded-lg border border-gray-200 dark:border-gray-700 flex items-center justify-center"
              >
                <UIcon
                  name="eos-icons:three-dots-loading"
                  class="w-8 h-8 text-gray-400"
                />
              </div>
            </div>

            <!-- Scroll hint -->
            <p class="text-xs text-gray-500 mt-2 text-center">
              {{ $t('imageSelect.scrollToSeeMore') }}
            </p>
          </div>
        </template>
      </UPopover>
    </div>

    <!-- Supported formats info -->
    <!-- <p class="text-xs text-gray-500">
      {{ $t("imageSelect.supportedFormats") }}: .png, .jpg, .webp
    </p> -->

    <!-- Hidden file input -->
    <input
      ref="fileInput"
      type="file"
      accept="image/*"
      :multiple="multiple"
      class="hidden"
      @change="handleFileChange"
    >
  </div>
</template>
