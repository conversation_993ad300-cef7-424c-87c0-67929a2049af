<script setup lang="ts">
const props = defineProps({
  disabled: {
    type: Boolean,
    default: false
  }
})

const { resolution, resolutionOptions } = useVideoResolution()
const { t } = useI18n()
</script>

<template>
  <URadioGroup
    v-model="resolution"
    orientation="horizontal"
    variant="card"
    value-key="value"
    :items="resolutionOptions"
    :disabled="disabled"
    size="xs"
    :ui="{
      fieldset: 'flex flex-wrap gap-2 flex-row',
      item: 'flex py-2 px-3 items-center justify-center rounded-lg border border-muted/50 hover:bg-muted/50 min-w-20'
    }"
  >
    <template #label="{ item }">
      <div class="flex flex-col items-center text-center">
        <div class="flex items-center gap-2">
          <UIcon
            name="material-symbols:high-quality"
            class="w-4 h-4"
          />
          <span class="text-sm font-medium">{{ item.label }}</span>
        </div>
      </div>
    </template>
  </URadioGroup>
</template>
