<template>
  <UModal
    v-model:open="isOpen"
    :ui="{
      wrapper: 'max-h-[90vh] overflow-auto !scrollbar-thin'
    }"
  >
    <template #content>
      <UForm
        ref="formRef"
        :schema="schema"
        :state="formData"
        @submit="authorize(onSubmit)"
      >
        <UCard
          :ui="{
            body: 'max-h-[75vh] overflow-y-auto p-6 !scrollbar-thin'
          }"
        >
          <template
            v-if="currentStep === 2"
            #header
          >
            <div class="flex flex-row justify-between items-center">
              <div class="flex items-center space-x-2">
                <UIcon
                  name="fluent:person-voice-24-filled"
                  class="w-5 h-5 text-primary-500"
                />
                <h2 class="text-xl font-semibold">
                  {{ $t("Create Custom Voice") }}
                </h2>
              </div>
              <UButton
                color="neutral"
                variant="ghost"
                icon="i-lucide-x"
                @click="closeModal"
              />
            </div>
          </template>

          <!-- Step 1: Voice Type Selection -->
          <div
            v-if="currentStep === 1"
            class="space-y-6"
          >
            <div class="text-center">
              <h3 class="text-lg font-medium mb-4">
                {{ $t("Type of voice to create") }}
              </h3>
            </div>

            <div class="grid grid-cols-1 gap-4">
              <!-- Instant Voice Cloning -->
              <div
                class="border rounded-lg p-4 cursor-pointer transition-all duration-200 hover:border-primary-500 hover:bg-primary-50 dark:hover:bg-primary-900/20"
                :class="{
                  'border-primary-500 bg-primary-50 dark:bg-primary-900/20':
                    selectedTrainingType === 'instant_voice',
                  'border-gray-200 dark:border-gray-700':
                    selectedTrainingType !== 'instant_voice'
                }"
                @click="selectedTrainingType = 'instant_voice'"
              >
                <div class="flex items-start space-x-3">
                  <URadio
                    v-model="selectedTrainingType"
                    value="instant_voice"
                    class="mt-1"
                  />
                  <div class="flex-1">
                    <h4 class="font-medium text-gray-900 dark:text-white">
                      {{ $t("Instant Voice Cloning") }}
                    </h4>
                    <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                      {{
                        $t(
                          "Clone a voice from a clean sample recording. Samples should contain 1 speaker and be over 1 minute long and not contain background noise"
                        )
                      }}
                    </p>
                  </div>
                </div>
              </div>

              <!-- Professional Voice Cloning -->
              <div
                class="border rounded-lg p-4 cursor-pointer transition-all duration-200 hover:border-primary-500 hover:bg-primary-50 dark:hover:bg-primary-900/20"
                :class="{
                  'border-primary-500 bg-primary-50 dark:bg-primary-900/20':
                    selectedTrainingType === 'professional_voice',
                  'border-gray-200 dark:border-gray-700':
                    selectedTrainingType !== 'professional_voice'
                }"
                @click="selectedTrainingType = 'professional_voice'"
              >
                <div class="flex items-start space-x-3">
                  <URadio
                    v-model="selectedTrainingType"
                    value="professional_voice"
                    class="mt-1"
                  />
                  <div class="flex-1">
                    <h4 class="font-medium text-gray-900 dark:text-white">
                      {{ $t("Professional Voice Cloning") }}
                    </h4>
                    <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                      {{
                        $t(
                          "Create the most realistic digital replica of your voice."
                        )
                      }}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div class="flex justify-end space-x-2">
              <UButton
                variant="outline"
                @click="closeModal"
              >
                {{ $t("Cancel") }}
              </UButton>
              <UButton
                :disabled="!selectedTrainingType"
                @click="nextStep"
              >
                {{ $t("Next") }}
              </UButton>
            </div>
          </div>

          <!-- Step 2: Voice Details Form -->
          <div
            v-else-if="currentStep === 2"
            class="space-y-4 pb-[45px]"
          >
            <div class="space-y-3">
              <!-- Speaker Name -->
              <UFormField
                :label="$t('Speaker Name')"
                name="speaker_name"
                required
              >
                <UInput
                  v-model="formData.speaker_name"
                  :placeholder="$t('Enter speaker name')"
                  class="w-full"
                  size="sm"
                  maxlength="50"
                />
                <template #hint>
                  <div class="flex justify-between text-xs text-gray-500">
                    <span>{{ formData.speaker_name.length }}/50</span>
                  </div>
                </template>
              </UFormField>

              <!-- Description -->
              <UFormField
                :label="$t('Description')"
                name="description"
              >
                <UTextarea
                  v-model="formData.description"
                  :placeholder="$t('Describe the voice characteristics')"
                  :rows="3"
                  class="w-full"
                  size="sm"
                  maxlength="500"
                />
                <template #hint>
                  <div class="flex justify-between text-xs text-gray-500">
                    <span>{{ formData.description.length }}/500</span>
                  </div>
                </template>
              </UFormField>
              <div class="grid grid-cols-1 md:grid-cols-3 gap-4 w-full">
                <!-- Gender -->
                <UFormField
                  :label="$t('Gender')"
                  name="gender"
                  required
                >
                  <USelect
                    v-model="formData.gender"
                    :items="genderOptions"
                    option-attribute="label"
                    value-attribute="value"
                    :placeholder="$t('Select gender')"
                    class="w-full"
                    size="sm"
                  />
                </UFormField>

                <!-- Age -->
                <UFormField
                  :label="$t('Age')"
                  name="age"
                  required
                >
                  <USelect
                    v-model="formData.age"
                    :items="ageOptions"
                    option-attribute="label"
                    value-attribute="value"
                    :placeholder="$t('Select age')"
                    class="w-full"
                    size="sm"
                  />
                </UFormField>

                <!-- Accent -->
                <UFormField
                  :label="$t('Accent')"
                  name="accent"
                  required
                >
                  <USelectMenu
                    v-model="formData.accent"
                    :items="accentOptions"
                    placeholder="Country"
                    value-key="value"
                    size="sm"
                    class="w-full md:min-w-32"
                    :ui="{
                      content: 'w-52'
                    }"
                  />
                </UFormField>
              </div>

              <!-- Audio File Upload -->
              <UFormField
                :label="$t('Audio Sample')"
                name="audioFile"
                required
              >
                <!-- Notes -->
                <div
                  class="mb-4 bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-lg px-4 py-2"
                >
                  <div class="flex items-start space-x-2">
                    <UIcon
                      name="heroicons:information-circle"
                      class="w-5 h-5 text-orange-500 mt-0.5"
                    />
                    <div class="text-sm text-orange-700 dark:text-orange-300">
                      <p class="font-medium mb-1">
                        {{ $t("Note:") }}
                      </p>
                      <ul class="space-y-1 text-xs">
                        <li>
                          {{
                            selectedTrainingType === "professional_voice"
                              ? $t(
                                "The sound should be clear, without any noise, and last at least 5 minutes to ensure good quality."
                              )
                              : $t(
                                "The sound should be clear, without any noise, and last at least 30 seconds to ensure good quality."
                              )
                          }}
                        </li>
                        <li>
                          {{ $t("Maximum file size: 150MB") }}
                        </li>
                        <li>
                          {{
                            selectedTrainingType === "professional_voice"
                              ? $t(
                                "It will cost {cost} credits each time you create a voice.",
                                { cost: 40 }
                              )
                              : $t(
                                "It will cost 0 credits each time you create a voice."
                              )
                          }}
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>
                <BaseFileSelect
                  v-if="!selectedAudioFile"
                  v-model="audioFiles"
                  :support-files="audioSupportFiles"
                  :support-files-display="audioSupportFilesDisplay"
                  :max-file-size="MAX_FILE_SIZE"
                  max-file-size-display="150MB"
                  @update:model-value="handleAudioFileSelected"
                />
                <div
                  v-else
                  class="border border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-4"
                >
                  <div class="flex items-center space-x-3">
                    <UIcon
                      name="heroicons:musical-note"
                      class="w-6 h-6 text-primary-500"
                    />
                    <div class="flex-1">
                      <p class="text-sm font-medium">
                        {{ selectedAudioFile.name }}
                      </p>
                      <p
                        class="text-xs"
                        :class="getFileSizeColor(selectedAudioFile.size)"
                      >
                        {{ formatFileSize(selectedAudioFile.size) }}
                        <span
                          v-if="selectedAudioFile.size > MAX_FILE_SIZE * 0.8"
                          class="ml-1"
                        >
                          ({{
                            Math.round(
                              (selectedAudioFile.size / MAX_FILE_SIZE) * 100
                            )
                          }}% of 150MB limit)
                        </span>
                      </p>
                      <p
                        v-if="audioDuration !== null"
                        class="text-xs"
                        :class="getAudioDurationColor(audioDuration)"
                      >
                        {{ $t("Duration") }}:
                        {{ formatDuration(audioDuration) }}
                        <span
                          v-if="!isAudioDurationValid(audioDuration)"
                          class="ml-1 text-red-500"
                        >
                          ({{ $t("Minimum required") }}:
                          {{ getMinDurationText() }})
                        </span>
                      </p>
                    </div>
                    <UButton
                      icon="heroicons:x-mark"
                      size="xs"
                      color="neutral"
                      variant="ghost"
                      @click="removeAudioFile"
                    />
                  </div>
                </div>
              </UFormField>

              <!-- Privacy Policy Checkbox -->
            </div>
          </div>
        </UCard>
        <template v-if="currentStep === 2">
          <div
            class="flex flex-row justify-between items-center px-6 sticky bottom-0 bg-white dark:bg-neutral-900 py-4 border-t border-gray-200 dark:border-neutral-700"
          >
            <UFormField name="agreeToPrivacy">
              <UCheckbox v-model="formData.agreeToPrivacy">
                <template #label>
                  <i18n-t keypath="I agree to the {0}">
                    <a
                      href="/privacy"
                      target="_blank"
                      class="text-primary-500"
                    >
                      {{ $t("Privacy Policy") }}
                    </a>
                  </i18n-t>
                </template>
              </UCheckbox>
            </UFormField>
            <div class="flex justify-end space-x-2">
              <UButton
                variant="outline"
                class="cursor-pointer"
                @click="previousStep"
              >
                {{ $t("Back") }}
              </UButton>
              <UButton
                type="submit"
                class="cursor-pointer"
                :loading="isLoading"
                :disabled="!isFormValid"
              >
                {{ $t("Generate") }}
              </UButton>
            </div>
          </div>
        </template>
      </UForm>
    </template>
  </UModal>
</template>

<script setup lang="ts">
import { z } from 'zod'

const { authorize } = useAuthorize()
const { voiceAccents } = useSpeechVoices()
interface Props {
  modelValue?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false
})

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'voiceCreated': [voice: any]
}>()

const { t } = useI18n()
const runtimeConfig = useRuntimeConfig()
const textToSpeechStore = useTextToSpeechStore()

// Modal state
const isOpen = computed({
  get: () => {
    // Prevent modal from opening in beta mode
    if (runtimeConfig.public.features.beta) {
      return false
    }
    return props.modelValue
  },
  set: value => emit('update:modelValue', value)
})

// Form state
const currentStep = ref(1)
const selectedTrainingType = ref('')
const formRef = ref()
const audioFiles = ref<File[]>([])
const selectedAudioFile = computed(() => audioFiles.value[0] || null)
const audioDuration = ref<number | null>(null)

// Form data
const formData = ref({
  speaker_name: '',
  description: '',
  gender: '',
  age: '',
  accent: '',
  agreeToPrivacy: false
})

// Loading state
const isLoading = computed(
  () => textToSpeechStore.loadings.createCustomVoice || false
)

// Audio file support
const audioSupportFiles = [
  'mp3',
  'wav',
  'flac',
  'audio/mpeg',
  'audio/wav',
  'audio/flac'
]
const audioSupportFilesDisplay = ['MP3', 'WAV', 'FLAC']

// Options
const genderOptions = computed(() => [
  { value: 'Male', label: t('Male') },
  { value: 'Female', label: t('Female') }
])

const ageOptions = [
  { label: t('Young'), value: 'Young' },
  { label: t('Middle'), value: 'Middle Aged' },
  { label: t('Old'), value: 'Old' }
]

const accentOptions = computed(() => {
  const accents = voiceAccents().map(accent => ({
    value: accent.value,
    label: accent.label,
    icon: accent.icon
  }))
  return [...accents]
})

// Constants
const MAX_FILE_SIZE = 150 * 1024 * 1024 // 150MB in bytes

// Form validation schema
const schema = computed(() =>
  z.object({
    speaker_name: z
      .string({
        required_error: t('validation.required')
      })
      .min(1, t('validation.required'))
      .max(50, t('Speaker name must be 50 characters or less')),
    description: z
      .string()
      .max(500, t('Description must be 500 characters or less')),
    gender: z
      .string({
        required_error: t('validation.required')
      })
      .min(1, t('validation.required')),
    age: z
      .string({
        required_error: t('validation.required')
      })
      .min(1, t('validation.required')),
    accent: z
      .string({
        required_error: t('validation.required')
      })
      .min(1, t('validation.required')),
    agreeToPrivacy: z.boolean().refine(val => val === true, {
      message: t('validation.mustAgreeToPrivacy')
    })
  })
)

// Computed properties
const isFormValid = computed(() => {
  const minDuration
    = selectedTrainingType.value === 'professional_voice' ? 300 : 30 // 5 minutes or 30 seconds
  const hasValidDuration
    = audioDuration.value !== null && audioDuration.value >= minDuration

  return (
    formData.value.speaker_name
    && formData.value.speaker_name.length <= 50
    && formData.value.description.length <= 500
    && selectedAudioFile.value
    && selectedAudioFile.value.size <= MAX_FILE_SIZE
    && hasValidDuration
    && formData.value.agreeToPrivacy
    && formData.value.gender
    && formData.value.age
    && formData.value.accent
  )
})

// Audio duration validation
const getAudioDuration = (file: File): Promise<number> => {
  return new Promise((resolve, reject) => {
    const audio = new Audio()
    const url = URL.createObjectURL(file)

    audio.addEventListener('loadedmetadata', () => {
      URL.revokeObjectURL(url)
      resolve(audio.duration)
    })

    audio.addEventListener('error', () => {
      URL.revokeObjectURL(url)
      reject(new Error('Failed to load audio file'))
    })

    audio.src = url
  })
}

const validateAudioDuration = async (file: File): Promise<boolean> => {
  try {
    const duration = await getAudioDuration(file)
    audioDuration.value = duration

    const minDuration
      = selectedTrainingType.value === 'professional_voice' ? 300 : 30 // 5 minutes or 30 seconds
    return duration >= minDuration
  } catch (error) {
    console.error('Error getting audio duration:', error)
    return false
  }
}

// Methods
const nextStep = () => {
  if (selectedTrainingType.value) {
    currentStep.value = 2
  }
}

const previousStep = () => {
  currentStep.value = 1
}

const closeModal = () => {
  isOpen.value = false
  resetForm()
}

const resetForm = () => {
  currentStep.value = 1
  selectedTrainingType.value = ''
  formData.value = {
    speaker_name: '',
    description: '',
    gender: '',
    age: '',
    accent: '',
    agreeToPrivacy: false
  }
  audioFiles.value = []
  audioDuration.value = null
}

const handleAudioFileSelected = async (files: File[]) => {
  if (files.length > 0) {
    const file = files[0]
    const toast = useToast()

    // Check file size
    if (file && file.size > MAX_FILE_SIZE) {
      toast.add({
        title: t('Error'),
        description: t('File size exceeds 150MB limit'),
        color: 'error'
      })
      return
    }

    // Check audio duration
    if (file) {
      const isValidDuration = await validateAudioDuration(file)
      if (!isValidDuration) {
        const minDurationText
          = selectedTrainingType.value === 'professional_voice'
            ? t('5 minutes')
            : t('30 seconds')

        toast.add({
          title: t('Error'),
          description: t('Audio duration must be at least {duration}', {
            duration: minDurationText
          }),
          color: 'error'
        })
        return
      }
    }
  }
  audioFiles.value = files
}

const removeAudioFile = () => {
  audioFiles.value = []
  audioDuration.value = null
}

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const getFileSizeColor = (fileSize: number) => {
  const percentage = (fileSize / MAX_FILE_SIZE) * 100
  if (percentage >= 100) {
    return 'text-red-500'
  } else if (percentage >= 80) {
    return 'text-orange-500'
  } else {
    return 'text-gray-500'
  }
}

const formatDuration = (seconds: number) => {
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = Math.floor(seconds % 60)
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
}

const isAudioDurationValid = (duration: number) => {
  const minDuration
    = selectedTrainingType.value === 'professional_voice' ? 300 : 30 // 5 minutes or 30 seconds
  return duration >= minDuration
}

const getAudioDurationColor = (duration: number) => {
  return isAudioDurationValid(duration) ? 'text-green-500' : 'text-red-500'
}

const getMinDurationText = () => {
  return selectedTrainingType.value === 'professional_voice'
    ? t('5 minutes')
    : t('30 seconds')
}

const onSubmit = async () => {
  if (!selectedAudioFile.value) return

  try {
    textToSpeechStore.loadings['createCustomVoice'] = true
    // Upload audio file first
    const uploadUrlRes = await textToSpeechStore.getUploadFileUrl(
      selectedAudioFile.value
    )
    await textToSpeechStore.uploadFile(
      selectedAudioFile.value,
      uploadUrlRes.url
    )

    // Create custom voice
    const payload = {
      speaker_name: formData.value.speaker_name,
      audio_path: uploadUrlRes.s3_file_path,
      description: formData.value.description,
      training_type: selectedTrainingType.value,
      gender: formData.value.gender,
      age: formData.value.age,
      accent: formData.value.accent
    }

    const result = await textToSpeechStore.createCustomVoice(payload)

    if (result) {
      emit('voiceCreated', result)
      // voices.value.unshift(result)
      useSpeechVoicesStore().addVoice(result)
      closeModal()

      // Show appropriate notification based on status
      const toast = useToast()
      if (result.status === 0) {
        // Status 0 = Training in progress
        toast.add({
          title: t('Voice Training Started'),
          description: t(
            'Your custom voice is being trained. You will be notified when it\'s ready.'
          ),
          color: 'warning'
        })

        // Start voice training sync if we have a voice ID
        // if (result.id) {
        //   const { startVoiceTrainingSync } = useVoiceTrainingSync()
        //   startVoiceTrainingSync({
        //     voiceId: result.id,
        //     intervalMs: 30000, // 30 seconds
        //     maxDurationMs: 1800000, // 30 minutes for voice training
        //     targetStatuses: [1, 3], // Complete (1) or Error (3)
        //     onStatusChange: (status, _voiceDetail) => {
        //       console.log(`🚀 ~ Voice training status update: ${status}`)
        //       // Voice is already updated in store by the sync mechanism
        //     },
        //     onComplete: (voiceDetail) => {
        //       console.log('🚀 ~ Voice training completed:', voiceDetail)

        //       // Show completion notification
        //       if (voiceDetail.status === 1) {
        //         toast.add({
        //           title: t('Voice Training Complete'),
        //           description: t('Your custom voice is ready to use!'),
        //           color: 'success'
        //         })
        //       } else if (voiceDetail.status === 3) {
        //         toast.add({
        //           title: t('Voice Training Failed'),
        //           description: t('Voice training failed. Please try again.'),
        //           color: 'error'
        //         })
        //       }
        //     },
        //     onError: (error) => {
        //       console.error('🚀 ~ Voice training sync error:', error)
        //     }
        //   })
        // }
      } else {
        // Status 1 = Success, Status 2 = Ready
        toast.add({
          title: t('Success'),
          description: t('Custom voice created successfully'),
          color: 'success'
        })
      }
    }
  } catch (error) {
    console.error('Error creating custom voice:', error)
    const toast = useToast()
    toast.add({
      title: t('Error'),
      description: t('Failed to create custom voice'),
      color: 'error'
    })
  }
}

// Watch for modal close to reset form
watch(isOpen, (newValue) => {
  if (!newValue) {
    resetForm()
  }
})
</script>
