<script setup lang="ts">
import { driver } from 'driver.js'
import { formatNumber } from '~/utils'
import {
  compareDialogArrays,
  commonValidationRules
} from '~/utils/generationValidation'

// SEO meta tags for AI dialogue generator page
useSeoMeta({
  title: 'AI Dialogue Generator - Create Conversations with GeminiGen AI',
  description:
    'Generate realistic AI dialogues and conversations between multiple speakers. Create podcasts, interviews, and multi-voice content with advanced AI.',
  ogTitle: 'AI Dialogue Generator - GeminiGen AI',
  ogDescription:
    'Create engaging conversations with our AI dialogue generator. Multiple voices, emotions, and natural speech patterns for professional content.',
  keywords:
    'AI dialogue generator, conversation AI, multi-speaker TTS, AI podcast generator, dialogue creation, voice conversation'
})

// Add structured data for dialogue generator page
useHead({
  script: [
    {
      type: 'application/ld+json',
      innerHTML: JSON.stringify({
        '@context': 'https://schema.org',
        '@type': 'WebApplication',
        'name': 'GeminiGen AI Dialogue Generator',
        'description': 'AI-powered dialogue and conversation generation tool',
        'applicationCategory': 'MultimediaApplication',
        'operatingSystem': 'Web Browser',
        'featureList': [
          'Multi-Speaker Dialogue Generation',
          'Conversation Creation',
          'Voice Customization',
          'Emotion Control',
          'Podcast Generation',
          'Interview Simulation',
          'Natural Speech Patterns'
        ],
        'offers': {
          '@type': 'Offer',
          'description': 'Dialogue generation service',
          'priceCurrency': 'USD'
        }
      })
    }
  ]
})

const { authorize } = useAuthorize()
const authStore = useAuthStore()
const user_credit = computed(() => authStore.$state.user?.user_credit)
const isAuthenticated = computed(
  () => !!authStore.$state.access_token && !!authStore.$state.user
)
const {
  model,
  models,
  speed,
  outputFormat,
  outputChannel,
  speedConfig,
  outputFormats
} = useSpeechGenModels()
const { loadVoices, selectedVoice, loading } = useSpeechVoices()
const { selectedEmotion } = useSpeechEmotions()
const router = useRouter()
const toast = useToast()
const { handleGeneration } = useGenerationConfirmation()
const { t } = useI18n()
const runtimeConfig = useRuntimeConfig()
const productStore = useProductStore()
const { getServicePriceByModelName } = storeToRefs(productStore)
const textToSpeechStore = useTextToSpeechStore()
const { hasSelectedFiles } = storeToRefs(textToSpeechStore)

const dialogToSpeechStore = useDialogToSpeechStore()
const {
  dialogs,
  voice1,
  voice2,
  custom_prompt,
  loadings,
  dialogResult,
  errors
} = storeToRefs(dialogToSpeechStore)

// Store initial values to compare for changes
const initialValues = ref({
  custom_prompt: '',
  model: models[0],
  voice1: null,
  voice2: null,
  selectedEmotion: null,
  speed: 1.0,
  outputFormat: 'mp3',
  outputChannel: 'mono',
  dialogs: []
})

// Initialize initial values on mount
onMounted(() => {
  loadVoices()
  initialValues.value = {
    custom_prompt: custom_prompt.value,
    model: model.value,
    voice1: voice1.value,
    voice2: voice2.value,
    selectedEmotion: selectedEmotion.value,
    speed: speed.value,
    outputFormat: outputFormat.value,
    outputChannel: outputChannel.value,
    dialogs: JSON.parse(JSON.stringify(dialogs.value))
  }
})

// Check if any values have changed from initial state
const hasChanges = computed(() => {
  // Basic field comparisons
  const basicFieldsChanged
    = custom_prompt.value !== initialValues.value.custom_prompt
      || model.value?.value !== initialValues.value.model?.value
      || voice1.value?.id !== initialValues.value.voice1?.id
      || voice2.value?.id !== initialValues.value.voice2?.id
      || selectedEmotion.value?.emotion_key
      !== initialValues.value.selectedEmotion?.emotion_key
      || speed.value !== initialValues.value.speed
      || outputFormat.value !== initialValues.value.outputFormat
      || outputChannel.value !== initialValues.value.outputChannel

  // Optimized dialog comparison - avoid JSON.stringify for better performance
  const dialogsChanged = compareDialogArrays(
    dialogs.value,
    initialValues.value.dialogs
  )

  return basicFieldsChanged || dialogsChanged
})

// Helper function to perform the actual generation
const performGeneration = async () => {
  const result = await handleDialogToSpeech()

  if (result) {
    // Calculate total text length from all dialogs
    const totalTextLength = dialogs.value.reduce((total, dialog) => {
      return total + (dialog.input?.length || 0)
    }, 0)

    // Determine wait time based on total text length
    const waitTime = totalTextLength > 1000 ? '3 minutes' : '30 seconds'

    toast.add({
      id: 'success',
      title: 'Dialog Generation',
      description: `We are starting to generate your dialog, please wait about ${waitTime}...`,
      color: 'success'
    })

    // Update initial values after successful generation
    initialValues.value = {
      custom_prompt: custom_prompt.value,
      model: model.value,
      voice1: voice1.value,
      voice2: voice2.value,
      selectedEmotion: selectedEmotion.value,
      speed: speed.value,
      outputFormat: outputFormat.value,
      outputChannel: outputChannel.value,
      dialogs: JSON.parse(JSON.stringify(dialogs.value))
    }
  }
}

// Calculate required credits for current dialog
const requiredCredits = computed(() => {
  return Math.round(
    (getServicePriceByModelName.value(model?.value, null)?.effective_price || 0) * (totalTextLength.value || 1)
  )
})

// Check if user has sufficient credits
const hasSufficientCredits = computed(() => {
  return (user_credit.value?.available_credit || 0) >= requiredCredits.value
})

const onGenerate = async () => {
  if (!isAuthenticated.value) {
    router.push('/auth/login')
    return
  }

  // Check credits before validation
  if (!hasSufficientCredits.value) {
    // Set error to show InsufficientCreditsError component
    errors.value.generateSpeech = 'NOT_ENOUGH_CREDIT'
    return
  }

  // Define validation rules
  const validationRules = [
    commonValidationRules.requiredArray(
      dialogs.value,
      t('Please add at least one dialog to generate speech.')
    ),
    () => ({
      isValid: dialogs.value.every(
        dialog => dialog.input?.trim().length >= 4
      ),
      message: t('Each dialog text must be at least 4 characters long.')
    }),
    () => ({
      isValid: dialogs.value.every(
        dialog => dialog.input?.trim().length <= 30000
      ),
      message: t('Each dialog text must not exceed 30,000 characters.')
    }),
    commonValidationRules.requiredMultiple(
      [voice1.value, voice2.value],
      t('Please select voices for both speakers.')
    )
  ]

  // Use the unified generation confirmation logic
  await handleGeneration({
    generationType: 'dialog',
    hasChanges,
    hasResult: computed(() => !!dialogResult.value),
    onGenerate: performGeneration,
    validationRules
  })
}

const handleDialogToSpeech = async () => {
  return await dialogToSpeechStore.generateDialogSpeech({
    model: model.value.value,
    emotion: selectedEmotion.value?.emotion_key,
    speed: speed.value,
    output_format: outputFormat.value,
    output_channel: outputChannel.value,
    custom_prompt: custom_prompt.value
  })
}

watch(
  () => hasSelectedFiles.value,
  (newValue) => {
    if (newValue) {
      prompt.value = t('Generate speech from selected file')
    } else {
      prompt.value = ''
      // focus to input
      nextTick(() => {
        const promptInput = document.querySelector(
          'textarea[placeholder*="dialog"]'
        )
        if (promptInput && promptInput instanceof HTMLElement) {
          promptInput.focus()
        }
      })
    }
  }
)

const showGuideSelectVoice = (hardShow = false) => {
  const driverObj = driver({
    showProgress: false,
    smoothScroll: true,
    showButtons: ['close', 'next'],
    nextBtnText: t('Next'),
    prevBtnText: t('Back'),
    doneBtnText: t('I got it!'),
    overlayClickBehavior: 'nextStep',
    stagePadding: 4,
    disableActiveInteraction: true,
    steps: [
      {
        element: '[data-tour="voices-library"]',
        popover: {
          title: t('Voices Library'),
          description: t('Select a voice for your speaker from the library.')
        }
      }
    ]
  })

  // check if guide is already shown
  if (localStorage.getItem('guide-select-voice') && !hardShow) return
  driverObj?.drive()
  localStorage.setItem('guide-select-voice', 'true')
}

const selectedSpeaker = ref(null) as Ref<'voice1' | 'voice2' | null>

const selectVoiceSuccess = ref(false)
const resetSuccessInstance = ref()
const onSelectVoice = (voice: SpeechVoice) => {
  if (selectedSpeaker.value === 'voice1') {
    dialogToSpeechStore.setVoice1(voice)
  } else if (selectedSpeaker.value === 'voice2') {
    dialogToSpeechStore.setVoice2(voice)
  }
  selectVoiceSuccess.value = true
  if (resetSuccessInstance.value) {
    clearTimeout(resetSuccessInstance.value)
  }
  resetSuccessInstance.value = setTimeout(() => {
    selectVoiceSuccess.value = false
  }, 2000)
}

const onSelectSpeaker = (speaker: 'voice1' | 'voice2') => {
  // check if user click again
  if (selectedSpeaker.value === speaker) {
    showGuideSelectVoice(true)
  }
  selectedSpeaker.value = speaker

  dialogResult.value = null
}

watch(
  () => selectedSpeaker.value,
  (newValue) => {
    if (newValue === 'voice1') {
      selectedVoice.value = voice1.value
    } else if (newValue === 'voice2') {
      selectedVoice.value = voice2.value
    }

    if (newValue) {
      showGuideSelectVoice()
    }
  }
)

const outputFormatItems = computed(() => {
  return outputFormats.map(format => ({
    label: format.label,
    value: format.value
  }))
})

const onSelectAnotherVoice = () => {
  dialogToSpeechStore.dialogResult = null
}

// Handle buy credits action
const handleBuyCredits = () => {
  errors.value.generateSpeech = null
  navigateTo('/profile/credits')
}

// Calculate total text length for credit calculation
const totalTextLength = computed(() => {
  return dialogs.value.reduce((total, dialog) => {
    return total + (dialog.input?.length || 0)
  }, 0)
})

const speechInsufficientCreditsMessage = computed(() => {
  return t('NOT_ENOUGH_CREDIT_MESSAGE_SPEECH_C', {
    money: 10,
    credit: '2000',
    characters: '500K',
    hours: 10,
    save: 80
  })
})
</script>

<template>
  <UContainer class="mt-0">
    <!-- SEO H1 Heading -->
    <div class="text-center mb-8">
      <h1 class="text-4xl font-bold text-gray-900 dark:text-white mb-4">
        {{ $t("AI Dialogue Generator") }}
      </h1>
      <p class="text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
        {{ $t("Create realistic conversations between multiple AI speakers") }}
      </p>
    </div>

    <div
      class="grid grid-cols-1 lg:grid-cols-2 sm:gap-4 lg:gap-6 space-y-8 sm:space-y-0"
    >
      <UCard>
        <div class="flex flex-col gap-6">
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <UFormField :label="$t('modelPreset')">
              <BaseModelSelect
                v-model="model"
                :models="models"
                class="w-full"
              />
            </UFormField>
            <UFormField
              v-if="model?.options?.includes('emotion')"
              :label="$t('emotion')"
            >
              <BaseSpeechEmotionSelectModal
                v-model="selectedEmotion"
                size="sm"
              />
            </UFormField>

            <UFormField :label="$t('Custom Prompt')">
              <BaseCustomPromptSelect v-model="custom_prompt" />
            </UFormField>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <UFormField
              v-if="model?.options?.includes('voice')"
              :label="$t('voice 1')"
              :hint="
                selectedSpeaker === 'voice1' ? $t('Select voice on right') : ''
              "
            >
              <UButton
                :label="
                  voice1?.speaker_name || $t('Select Voice for speaker 1')
                "
                :icon="
                  selectVoiceSuccess && selectedSpeaker === 'voice1'
                    ? 'line-md:confirm-circle-twotone'
                    : 'ri:user-voice-line'
                "
                :color="selectedSpeaker === 'voice1' ? 'primary' : 'neutral'"
                variant="outline"
                :trailing-icon="
                  selectedSpeaker === 'voice1'
                    ? 'lucide:chevron-right'
                    : 'lucide:chevron-right'
                "
                class="w-full"
                size="sm"
                :ui="{
                  trailingIcon: 'ml-auto'
                }"
                :disabled="loading"
                @click="onSelectSpeaker('voice1')"
              />
            </UFormField>
            <UFormField
              v-if="model?.options?.includes('voice')"
              :label="$t('voice 2')"
              :hint="
                selectedSpeaker === 'voice2' ? $t('Select voice on right') : ''
              "
            >
              <UButton
                :label="
                  voice2?.speaker_name || $t('Select Voice for speaker 2')
                "
                :icon="
                  selectVoiceSuccess && selectedSpeaker === 'voice2'
                    ? 'line-md:confirm-circle-twotone'
                    : 'ri:user-voice-line'
                "
                :color="selectedSpeaker === 'voice2' ? 'primary' : 'neutral'"
                variant="outline"
                :trailing-icon="
                  selectedSpeaker === 'voice2'
                    ? 'lucide:chevron-right'
                    : 'lucide:chevron-right'
                "
                class="w-full"
                size="sm"
                :ui="{
                  trailingIcon: 'ml-auto'
                }"
                :disabled="loading"
                @click="onSelectSpeaker('voice2')"
              />
            </UFormField>
          </div>
          <UFormField :label="$t('Dialog Content')">
            <div class="flex flex-col gap-4">
              <BaseSpeakerGen
                v-for="(dialog, index) in dialogs"
                :key="index"
                :can-remove="index !== 0 || dialogs.length > 1"
                v-bind="dialog"
                @update:model-value="
                  dialogToSpeechStore.updateDialog(index, $event)
                "
                @remove="dialogToSpeechStore.removeDialog(index)"
              />
              <UButton
                color="neutral"
                variant="soft"
                icon="icons8:plus"
                :label="$t('Add dialog')"
                class="w-full sm:w-fit"
                @click="dialogToSpeechStore.addDialog()"
              />
            </div>
          </UFormField>
          <div class="flex flex-row gap-6">
            <!-- Speed Settings -->
            <UFormField
              v-if="model?.options?.includes('speed')"
              :label="$t('speed')"
              class="flex-1"
            >
              <div class="flex flex-col gap-3">
                <UInputNumber
                  v-model="speed"
                  :min="speedConfig.min"
                  :max="speedConfig.max"
                  :step="speedConfig.step"
                  size="sm"
                  class="w-full"
                />
                <USlider
                  v-model="speed"
                  :min="speedConfig.min"
                  :max="speedConfig.max"
                  :step="speedConfig.step"
                  class="w-full"
                />
                <div class="flex justify-between text-xs text-gray-400">
                  <span>{{ speedConfig.min }}x</span>
                  <span>{{ speedConfig.max }}x</span>
                </div>
              </div>
            </UFormField>

            <!-- Output Format Settings -->
            <UFormField
              v-if="model?.options?.includes('outputFormat')"
              :label="$t('outputFormat')"
            >
              <URadioGroup
                v-model="outputFormat"
                orientation="vertical"
                variant="card"
                value-key="value"
                :items="outputFormatItems"
                size="xs"
              />
            </UFormField>
          </div>

          <div class="flex justify-end gap-2 items-center flex-row">
            <div
              class="text-xs text-right"
            >
              <div>
                {{
                  $t("Credits: {credits} remaining", {
                    credits: formatNumber(user_credit?.available_credit || 0)
                  })
                }}
              </div>
              <div class="text-primary">
                {{
                  $t("Price per 1 character: {cost} Credits", {
                    cost:
                      getServicePriceByModelName(model?.value, null)
                        ?.effective_price || 0
                  })
                }}
              </div>
              <div
                v-if="totalTextLength > 0"
                :class="[
                  'font-semibold',
                  hasSufficientCredits ? 'text-primary' : 'text-error'
                ]"
              >
                {{
                  $t("This generation will cost: {cost} Credits", {
                    cost: formatNumber(requiredCredits)
                  })
                }}
              </div>
              <div
                v-if="totalTextLength > 0 && !hasSufficientCredits"
                class="text-error text-xs"
              >
                {{ $t("Insufficient credits. Please buy more credits to continue.") }}
              </div>
            </div>
            <UButton
              color="primary"
              :label="$t('Generate')"
              class="bg-gradient-to-r from-primary-500 to-primary-500 max-h-10 dark:text-white hover:from-primary-600 hover:to-success-600 !cursor-pointer"
              trailing-icon="mingcute:ai-fill"
              :loading="loadings['generateSpeech']"
              :disabled="!dialogs.some((d) => d.input.trim()) || !hasSufficientCredits"
              @click="authorize(onGenerate)"
            />
          </div>
        </div>
      </UCard>

      <Motion
        v-if="
          (dialogResult || loadings['generateSpeech'])
            && !errors['generateSpeech']
        "
        :initial="{
          scale: 1.1,
          opacity: 0,
          filter: 'blur(20px)'
        }"
        :animate="{
          scale: 1,
          opacity: 1,
          filter: 'blur(0px)'
        }"
        :transition="{
          duration: 0.6,
          delay: 0.5
        }"
      >
        <AIToolDialogCard
          v-bind="dialogResult"
          :data="dialogResult"
          :loading="loadings['generateSpeech']"
          class="h-fit sticky top-20"
          @select-another-voice="onSelectAnotherVoice"
        />
      </Motion>
      <VoicesLibraries
        v-else-if="!errors['generateSpeech']"
        data-tour="voices-library"
        class="hidden lg:block h-fit sticky top-20"
        show-only-gemini-voices
        @select-voice="onSelectVoice"
      />
      <UCard
        v-if="errors['generateSpeech']"
        :ui="{
          body: 'h-full dark:text-muted/40'
        }"
      >
        <div class="flex flex-col items-center justify-center h-full p-8">
          <!-- Enhanced display for NOT_ENOUGH_AND_LOCK_CREDIT error -->
          <InsufficientCreditsError
            v-if="
              ['NOT_ENOUGH_CREDIT', 'NOT_ENOUGH_AND_LOCK_CREDIT'].includes(
                errors['generateSpeech']
              )
            "
            :credits-needed="requiredCredits"
            :available-credits="user_credit?.available_credit || 0"
            generation-type="dialog"
            :message="speechInsufficientCreditsMessage"
            @buy-credits="handleBuyCredits"
            @view-pricing="$router.push('/pricing')"
          />

          <!-- Default error display for other errors -->
          <div
            v-else
            class="text-center space-y-4"
          >
            <div>
              <UIcon
                name="i-lucide-alert-circle"
                class="text-6xl mb-2 text-error"
              />
            </div>
            <div class="text-sm text-error">
              {{ $t(errors["generateSpeech"] || "Something went wrong") }}
            </div>
          </div>
        </div>
      </UCard>
      <UCard
        v-else-if="false"
        :ui="{
          body: 'h-full dark:text-muted/40'
        }"
      >
        <div class="flex flex-col items-center justify-center h-full">
          <div>
            <UIcon
              name="i-lucide-users"
              class="text-6xl mb-2"
            />
          </div>
          <div class="text-sm">
            {{ $t("Your generated dialog will appear here") }}
          </div>
        </div>
      </UCard>
    </div>
  </UContainer>
</template>
