#!/usr/bin/env node

/**
 * Script to test sitemap generation locally
 * Run with: node scripts/test-sitemap-local.js
 */

const http = require('http')

const baseUrl = 'http://localhost:3000'

// Sitemap URLs to test
const sitemapUrls = [
  '/sitemap-index.xml',
  '/sitemap.xml',
  '/sitemap-docs.xml',
  '/sitemap-blog.xml'
]

function testUrl(url) {
  return new Promise((resolve) => {
    const fullUrl = `${baseUrl}${url}`
    
    const req = http.get(fullUrl, (res) => {
      let data = ''
      res.on('data', chunk => data += chunk)
      res.on('end', () => {
        resolve({
          url,
          status: res.statusCode,
          success: res.statusCode >= 200 && res.statusCode < 400,
          contentType: res.headers['content-type'],
          hasXml: data.includes('<?xml'),
          size: data.length
        })
      })
    })
    
    req.on('error', (err) => {
      resolve({
        url,
        status: 'ERROR',
        success: false,
        error: err.message
      })
    })
    
    req.setTimeout(5000, () => {
      req.destroy()
      resolve({
        url,
        status: 'TIMEOUT',
        success: false,
        error: 'Request timeout'
      })
    })
  })
}

async function testSitemaps() {
  console.log('🔍 Testing local sitemap URLs...\n')
  console.log(`📍 Base URL: ${baseUrl}\n`)
  
  // Test sitemap files
  console.log('📄 Testing sitemap files:')
  const results = []
  
  for (const url of sitemapUrls) {
    const result = await testUrl(url)
    results.push(result)
    
    const status = result.success ? '✅' : '❌'
    console.log(`${status} ${url}`)
    console.log(`   Status: ${result.status}`)
    console.log(`   Content-Type: ${result.contentType || 'N/A'}`)
    console.log(`   Has XML: ${result.hasXml ? 'Yes' : 'No'}`)
    console.log(`   Size: ${result.size || 0} bytes`)
    
    if (result.error) {
      console.log(`   Error: ${result.error}`)
    }
    console.log('')
  }
  
  // Summary
  const successful = results.filter(r => r.success).length
  const total = results.length
  
  console.log('📊 Summary:')
  console.log(`✅ Successful: ${successful}/${total}`)
  console.log(`❌ Failed: ${total - successful}/${total}`)
  
  if (successful === total) {
    console.log('\n🎉 All sitemap URLs are accessible!')
    console.log('✅ Ready for static deployment!')
  } else {
    console.log('\n⚠️  Some sitemap URLs failed. Please check the errors above.')
  }
}

console.log('🚀 GeminiGen AI Local Sitemap Tester')
console.log('===================================\n')

testSitemaps().catch(console.error)
