# SEO & Sitemap Guide for GeminiGen AI

## 📋 Overview

This guide covers the SEO optimization and sitemap implementation for GeminiGen AI platform to improve Google Search visibility and ranking.

## 🗺️ Sitemap Structure

### Main Sitemaps

1. **Sitemap Index** (`/sitemap-index.xml`)
   - Central index pointing to all sitemap files
   - Automatically updated when new sitemaps are added

2. **Main Sitemap** (`/sitemap.xml`)
   - Contains all main application pages
   - Includes AI tools, authentication, and business pages
   - Priority and frequency optimized for each page type

3. **Documentation Sitemap** (`/sitemap-docs.xml`)
   - Dynamic sitemap for documentation content
   - Automatically includes content from `/content` directory
   - Fallback for manual documentation routes

4. **Blog Sitemap** (`/sitemap-blog.xml`)
   - Prepared for future blog content
   - Currently includes main blog page
   - Ready for individual blog post URLs

### URL Priorities

- **Homepage** (`/`): Priority 1.0, Daily updates
- **AI Tools** (`/app/*`): Priority 0.9, Weekly updates
- **Pricing** (`/pricing`): Priority 0.8, Monthly updates
- **Public Pages**: Priority 0.7, Monthly updates
- **Auth Pages**: Priority 0.6, Monthly updates
- **Legal Pages**: Priority 0.4, Yearly updates

## 🔧 SEO Implementation

### Meta Tags & Open Graph

All pages include comprehensive meta tags:
- Title, description, keywords
- Open Graph tags for social sharing
- Twitter Card metadata
- Canonical URLs
- Robots directives

### Structured Data (JSON-LD)

Implemented structured data types:
- **Organization**: Company information
- **Website**: Site-wide search functionality
- **Product**: AI tools and services
- **Article**: Blog posts and documentation
- **FAQ**: Frequently asked questions
- **Breadcrumbs**: Navigation structure

### SEO Composable (`useSEO.ts`)

Utility functions for SEO management:
```typescript
const { setPageSEO, setProductStructuredData, setBreadcrumbs } = useSEO()

// Set page-specific SEO
setPageSEO({
  title: 'Custom Page Title',
  description: 'Page description',
  keywords: 'relevant, keywords',
  type: 'product'
})
```

## 🚀 Implementation Details

### Static Sitemap Generation

Sitemaps are generated as static files for deployment:
- Generated during build process
- Current date `lastmod` values
- Proper XML formatting
- Served as static files from `/public` directory

**Important**: This project uses static site generation (`ssr: false`) and deploys only the `public` directory. Server routes don't work in this setup, so we use static XML files instead.

### robots.txt Configuration

Located at `/public/robots.txt`:
- Allows all main pages
- Disallows private user areas
- Points to sitemap index
- Includes crawl delay

### Nuxt.config.ts SEO Settings

Enhanced configuration includes:
- Comprehensive meta tags
- Open Graph properties
- Twitter Card settings
- Canonical URL structure
- Sitemap references

## 📊 Testing & Validation

### Build Process

Generate static sitemaps before build:
```bash
npm run seo:generate
```

Or use the integrated build command:
```bash
npm run generate  # Automatically generates sitemaps first
```

### Test Scripts

Test sitemaps locally:
```bash
npm run seo:test:local
```

Test production sitemaps:
```bash
npm run seo:test
```

This script:
- Tests all sitemap URLs
- Validates page accessibility
- Checks XML structure
- Provides success/failure summary

### Manual Testing

1. **Sitemap Index**: Visit `/sitemap-index.xml`
2. **Individual Sitemaps**: Check each sitemap URL
3. **Google Search Console**: Submit sitemaps
4. **Rich Results Test**: Validate structured data

## 🎯 SEO Best Practices Implemented

### Technical SEO
- ✅ XML Sitemaps
- ✅ robots.txt
- ✅ Canonical URLs
- ✅ Meta descriptions
- ✅ Structured data
- ✅ Open Graph tags
- ✅ Mobile-friendly viewport
- ✅ Fast loading times

### Content SEO
- ✅ Descriptive page titles
- ✅ Unique meta descriptions
- ✅ Relevant keywords
- ✅ Header hierarchy (H1, H2, H3)
- ✅ Alt text for images
- ✅ Internal linking

### Performance SEO
- ✅ Optimized images
- ✅ Minified CSS/JS
- ✅ Gzip compression
- ✅ Browser caching
- ✅ CDN usage

## 🔄 Maintenance

### Regular Tasks

1. **Update Sitemaps**: Add new pages to relevant sitemaps
2. **Monitor Performance**: Check Google Search Console
3. **Update Meta Tags**: Refresh descriptions and keywords
4. **Test URLs**: Run test script regularly
5. **Structured Data**: Validate with Google's Rich Results Test

### Adding New Pages

1. Add URL to appropriate sitemap file
2. Set proper priority and change frequency
3. Implement page-specific SEO meta tags
4. Add structured data if applicable
5. Update robots.txt if needed

## 📈 Expected Results

With proper implementation, expect:
- Improved Google Search visibility
- Better click-through rates from search results
- Enhanced social media sharing
- Faster indexing of new content
- Higher search engine rankings

## 🛠️ Tools & Resources

- [Google Search Console](https://search.google.com/search-console)
- [Google Rich Results Test](https://search.google.com/test/rich-results)
- [Schema.org Documentation](https://schema.org/)
- [Open Graph Protocol](https://ogp.me/)
- [Twitter Card Validator](https://cards-dev.twitter.com/validator)

## 📞 Support

For SEO-related questions or issues:
1. Check this documentation
2. Run the test script
3. Validate with Google tools
4. Contact the development team
