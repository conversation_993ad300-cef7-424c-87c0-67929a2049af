<template>
  <div class="max-w-2xl mx-auto p-8">
    <div class="mb-8">
      <h1 class="text-2xl font-bold mb-2">
        BaseImageSelect Demo
      </h1>
      <p class="text-gray-600">
        Test the enhanced BaseImageSelect component with UPopover and history import functionality.
      </p>
    </div>

    <div class="space-y-8">
      <!-- Single image selection -->
      <div class="border rounded-lg p-6">
        <h2 class="text-lg font-semibold mb-4">
          Single Image Selection
        </h2>
        <BaseImageSelect
          v-model="singleImage"
          :multiple="false"
        />

        <div
          v-if="singleImage.length > 0"
          class="mt-4"
        >
          <h3 class="text-md font-medium mb-2">
            Selected Image:
          </h3>
          <div class="flex items-center gap-4">
            <img
              :src="singleImage[0].src"
              :alt="singleImage[0].alt"
              class="w-20 h-20 object-cover rounded border"
            >
            <div>
              <p class="font-medium">
                {{ singleImage[0].alt }}
              </p>
              <p class="text-sm text-gray-500">
                {{ singleImage[0].file ? 'Uploaded file' : 'From history' }}
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- Multiple image selection -->
      <div class="border rounded-lg p-6">
        <h2 class="text-lg font-semibold mb-4">
          Multiple Image Selection
        </h2>
        <BaseImageSelect
          v-model="multipleImages"
          :multiple="true"
        />

        <div
          v-if="multipleImages.length > 0"
          class="mt-4"
        >
          <h3 class="text-md font-medium mb-2">
            Selected Images ({{ multipleImages.length }}):
          </h3>
          <div class="grid grid-cols-3 gap-4">
            <div
              v-for="(image, index) in multipleImages"
              :key="index"
              class="relative"
            >
              <img
                :src="image.src"
                :alt="image.alt"
                class="w-full aspect-square object-cover rounded border"
              >
              <div class="mt-1">
                <p class="text-xs font-medium truncate">
                  {{ image.alt }}
                </p>
                <p class="text-xs text-gray-500">
                  {{ image.file ? 'Upload' : 'History' }}
                </p>
              </div>
              <button
                class="absolute top-1 right-1 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600"
                @click="removeImage(index)"
              >
                ×
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Debug info -->
      <div class="border rounded-lg p-6 bg-gray-50">
        <h2 class="text-lg font-semibold mb-4">
          Debug Info
        </h2>

        <div class="space-y-4">
          <div>
            <h3 class="font-medium">
              Single Image Data:
            </h3>
            <pre class="bg-white p-2 rounded text-xs overflow-auto">{{ JSON.stringify(singleImage, null, 2) }}</pre>
          </div>

          <div>
            <h3 class="font-medium">
              Multiple Images Data:
            </h3>
            <pre class="bg-white p-2 rounded text-xs overflow-auto">{{ JSON.stringify(multipleImages, null, 2) }}</pre>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

// SEO for demo page
useSeoMeta({
  title: 'BaseImageSelect Demo - Image Selection Component Test',
  description: 'Demo page for testing BaseImageSelect component with UPopover and history import functionality'
})

// Reactive data
const singleImage = ref([])
const multipleImages = ref([])

// Helper function to remove image
const removeImage = (index) => {
  multipleImages.value.splice(index, 1)
}
</script>
