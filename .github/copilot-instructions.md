# Imagen Frontend - AI Coding Instructions

## Project Architecture

This is a **Nuxt 3 SPA** (`ssr: false`) for GeminiGen AI - a multi-service AI generation platform for video, image, speech, and text synthesis. The app uses Supabase for authentication and a custom REST API backend.

### Key Stack Components
- **Nuxt 3** with SPA mode, TypeScript, and auto-imports
- **Pinia** stores with persistence via `pinia-plugin-persistedstate`
- **Nuxt UI Pro** + Tailwind CSS for components and styling
- **Nuxt i18n** for 8-language internationalization (default: Vietnamese, reference: English)
- **Supabase** for authentication with custom JWT token handling
- **Axios** with automatic token refresh interceptors
- **PNPM** package manager

## Critical Developer Workflows

### Development Commands
```bash
pnpm dev          # Development server on localhost:3000
pnpm generate     # Static generation with sitemap pre-generation
pnpm i18n:check   # Check translation completeness across locales
pnpm i18n:add-missing  # Auto-add missing translation keys
pnpm typecheck    # Vue + TypeScript validation
```

### Build & Deployment
- **Cloudflare Pages** deployment via `deploy.sh` script with cache invalidation
- **Docker** multi-stage build with Node.js 20.16.0
- **Basic auth** for dev/staging environments (auto-configured in `_middleware.js`)

## Core Patterns & Conventions

### Store Architecture (Pinia)
All stores follow consistent patterns with persistence:
```typescript
// Example: stores/auth.ts pattern
export const useAuthStore = defineStore('authStore', {
  state: () => ({
    user: null,
    access_token: null,
    loadings: {} as Record<string, boolean>,
    errors: {} as Record<string, any>
  }),
  persist: [{ picks: ['user', 'access_token'], storage: localStorage }],
  getters: {
    isAuthenticated: state => !!state.access_token && !!state.user,
    isPremiumUser: state => state.user?.user_plan?.product?.id === 'PP0001'
  }
})
```

### API Layer (`composables/useAPI.ts`)
- Centralized Axios instance with auth interceptors
- Automatic token refresh on 401/403 responses
- Global error handling with toast notifications
- Request queuing during token refresh

### Component Organization
```
components/
├── ai-tool/           # AI generation forms and UI
├── base/             # Reusable base components  
├── common/           # Shared utility components
├── layout/           # Layout-specific components
└── [feature]/        # Feature-specific components
```

### Page Structure & SEO
Every AI tool page implements:
```vue
<script setup>
// SEO meta tags
useSeoMeta({
  title: 'AI Video Generator - Create Videos from Text',
  description: '...',
  keywords: 'AI video generator, text to video...'
})

// Structured data for rich snippets
useHead({
  script: [{
    type: 'application/ld+json',
    innerHTML: JSON.stringify({
      '@context': 'https://schema.org',
      '@type': 'WebApplication',
      'name': 'GeminiGen AI Video Generator'
    })
  }]
})
</script>
```

## Internationalization (i18n) System

### Translation Workflow
1. **Always** add new keys to `i18n/locales/en.json` (reference locale)
2. Run `pnpm i18n:add-missing` to propagate placeholders
3. Replace English placeholders with proper translations
4. Use `pnpm i18n:check` to verify completeness

### Locale Configuration
- **Default**: Vietnamese (`vi`)
- **Reference**: English (`en`) 
- **8 total locales**: en, vi, zh, ja, es, fr, de, pt
- Locale persistence in localStorage via app store

## Integration Points

### Authentication Flow
1. **Supabase** for initial auth (Google OAuth, email/password)
2. **Custom JWT** tokens from backend API (`/signup`, `/login`)
3. **Automatic refresh** via `useAPI` interceptors
4. **Persistent auth state** in Pinia store

### AI Service Integration
- **Video Generation**: Veo 2/3 models via `/api/video-gen`
- **Image Generation**: Imagen, NanoBanana via `/api/image-gen` 
- **Speech Synthesis**: Multiple voice models via `/api/text-to-speech`
- **Credit System**: User credits tracked per generation

### External Services
- **PayPal**: Payment processing for credit purchases
- **Cloudflare**: CDN and Pages hosting with cache invalidation
- **Google Analytics**: Tracking via gtag events

## Development Guidelines

### TypeScript Usage
- **Strict mode enabled** with `@typescript-eslint/no-explicit-any: off`
- **Auto-imports** for Nuxt composables, utilities, and stores
- **Type-safe** i18n with locale string unions

### Component Patterns
- **Composition API** with `<script setup>`
- **Auto-imported** composables (`useAuthStore`, `useToast`, etc.)
- **Consistent** loading states and error handling via store patterns
- **Reactive** forms with validation utilities in `utils/generationValidation`

### Styling Approach
- **Nuxt UI Pro** components as primary building blocks
- **Tailwind CSS** for custom styling
- **Dark/light mode** support via `useColorMode`
- **Responsive design** with mobile-first approach

### Error Handling
- **Global error interceptor** in `useAPI` composable
- **Per-action error states** in store `errors` object
- **Toast notifications** for user-facing errors
- **Graceful fallbacks** for API failures

## File Naming & Structure
- **kebab-case** for files and directories
- **PascalCase** for Vue components
- **camelCase** for composables, stores, and utilities
- **Nested routing** follows Nuxt 3 file-based routing in `pages/`

This architecture enables rapid AI feature development while maintaining consistency across the multi-service platform.

Using Nuxt UI documentation from https://ui.nuxt.com/llms.txt
