{"system": "Générateur d'Images IA", "helloWorld": "Bonjour le monde !", "Describe the image you want to generate...": "Décrivez l'image que vous voulez générer...", "appTitle": "Création d'Images IA", "copyright": "Copyright © {year}, Création d'Images IA", "available": "Disponible pour de nouveaux projets", "notAvailable": "Non disponible pour le moment", "blog": "Blog", "copyLink": "Copier le lien", "minRead": "MIN LECTURE", "articleLinkCopied": "Lien de l'article copié dans le presse-papiers", "clickToClose": "Cliquez n'importe où ou appuyez sur ESC pour fermer", "promptDetails": "<PERSON><PERSON><PERSON> du prompt", "generateWithPrompt": "Générer avec ce prompt", "generateWithSettings": "Générer avec ces paramètres", "preset": "Préréglage", "style": "Style", "resolution": "Résolution", "addImage": "Ajouter une Image", "modelPreset": "Modèle/Préréglage", "imageDimensions": "Dimensions de l'Image", "yourImage": "Votre Image", "generate": "<PERSON><PERSON><PERSON><PERSON>", "nav.aitool": "Outil IA", "nav.api": "API", "nav.login": "Connexion", "nav.history": "Histoire", "nav.orders": "Commandes", "3D Render": "Rendu 3D", "Acrylic": "Acrylique", "Anime General": "<PERSON><PERSON>", "Creative": "<PERSON><PERSON><PERSON><PERSON>", "Dynamic": "Dynamique", "Fashion": "Mode", "Game Concept": "Concept de Jeu", "Graphic Design 3D": "Design Graphique 3D", "Illustration": "Illustration", "None": "Aucun", "Portrait": "Portrait", "Portrait Cinematic": "Portrait Cinématographique", "Portrait Fashion": "Portrait de Mode", "Ray Traced": "<PERSON><PERSON><PERSON>", "Stock Photo": "Photo de Stock", "Watercolor": "<PERSON><PERSON><PERSON><PERSON>", "AI Image Generator": "Générateur d'Images IA", "Generate AI images from text prompts with a magical particle transformation effect": "Générez des images IA à partir de prompts texte avec un effet magique de transformation de particules", "Enter your prompt": "Entrez votre prompt", "Generating...": "Génération...", "Generate Image": "Générer l'Image", "Enter a prompt and click Generate Image to create an AI image": "Entrez un prompt et cliquez sur Générer l'Image pour créer une image IA", "Prompt:": "Prompt :", "Download": "Télécharger", "How It Works": "Comment ça Marche", "This AI image generator uses a particle-based transformation effect to visualize the creation process. When you enter a prompt and click 'Generate', the system:": "Ce générateur d'images IA utilise un effet de transformation basé sur les particules pour visualiser le processus de création. Lorsque vous entrez un prompt et cliquez sur 'Générer', le système :", "Sends your prompt to an AI image generation API": "Envoie votre prompt à une API de génération d'images IA", "Creates a particle system with thousands of tiny particles": "Crée un système de particules avec des milliers de petites particules", "Transforms the random noise particles into the generated image": "Transforme les particules de bruit aléatoire en image générée", "The particles start in a random noise pattern and then smoothly transform into the final image, creating a magical effect that simulates the AI's creative process.": "Les particules commencent dans un motif de bruit aléatoire puis se transforment en douceur en image finale, créant un effet magique qui simule le processus créatif de l'IA.", "Transform": "Transformer", "Transforming...": "Transformation...", "Initializing particles...": "Initialisation des particules...", "Loading image...": "Chargement de l'image...", "Creating particle system...": "Création du système de particules...", "Adding event listeners...": "Ajout des écouteurs d'événements...", "Ready!": "<PERSON><PERSON><PERSON><PERSON> !", "AI Image Particle Effect": "Effet de Particules d'Image IA", "A demonstration of the BaseMagicImage component that transforms particles into AI-generated images": "Une démonstration du composant BaseMagicImage qui transforme les particules en images générées par IA", "Click anywhere or press ESC to close": "Cliquez n'importe où ou appuyez sur ESC pour fermer", "auth.login": "Connexion", "auth.loginDescription": "Connectez-vous à votre compte pour continuer", "auth.email": "Email", "auth.enterEmail": "Entrez votre email", "auth.password": "Mot de passe", "auth.enterPassword": "Entrez votre mot de passe", "auth.rememberMe": "Se souvenir de moi", "auth.welcomeBack": "Bon retour", "auth.signupFailed": "Inscription échouée", "auth.signupFailedDescription": "Il y a eu une erreur lors de l'inscription. Veuillez réessayer.", "auth.dontHaveAccount": "Vous n'avez pas de compte ?", "auth.signUp": "S'inscrire", "auth.forgotPassword": "Mot de passe oublié ?", "auth.bySigningIn": "En vous connectant, vous acceptez nos", "auth.termsOfService": "Conditions de Service", "auth.signUpTitle": "S'inscrire", "auth.signUpDescription": "C<PERSON>ez un compte pour commencer", "auth.name": "Nom", "auth.enterName": "Entrez votre nom", "auth.createAccount": "<PERSON><PERSON><PERSON> un compte", "auth.alreadyHaveAccount": "Vous avez déjà un compte ?", "auth.bySigningUp": "En vous inscrivant, vous acceptez nos", "auth.backToHome": "Retour à l'accueil", "auth.notVerifyAccount": "Votre compte n'est pas vérifié. Veuillez vérifier votre compte pour continuer", "auth.verifyAccount": "Vérifier le compte", "auth.resendActivationEmail": "Renvoyer l'email d'activation", "auth.accountRecovery": "Récupération de Compte", "auth.accountRecoveryTitle": "Récupérez votre compte", "auth.accountRecoveryDescription": "Entrez votre email pour recevoir des instructions de réinitialisation de mot de passe", "auth.sendRecoveryEmail": "Envoyer l'email de récupération", "auth.recoveryEmailSent": "Email de récupération envoyé", "auth.recoveryEmailSentDescription": "Veuillez vérifier votre email pour les instructions de réinitialisation de mot de passe", "auth.resetPassword": "Réinitialiser le Mot de Passe", "auth.resetPasswordTitle": "Réinitialisez votre mot de passe", "auth.resetPasswordDescription": "Entrez votre nouveau mot de passe", "auth.newPassword": "Nouveau mot de passe", "auth.confirmPassword": "Confirmer le mot de passe", "auth.enterNewPassword": "Entrez votre nouveau mot de passe", "auth.enterConfirmPassword": "Confirmez votre nouveau mot de passe", "auth.passwordResetSuccess": "Réinitialisation de mot de passe réussie", "auth.passwordResetSuccessDescription": "Votre mot de passe a été réinitialisé avec succès. Vous pouvez maintenant vous connecter avec votre nouveau mot de passe", "auth.activateAccount": "<PERSON><PERSON>", "auth.activateAccountTitle": "Activez votre compte", "auth.activateAccountDescription": "Votre compte est en cours d'activation...", "auth.accountActivated": "Compte activé", "auth.accountActivatedDescription": "Votre compte a été activé avec succès. Vous pouvez maintenant vous connecter", "auth.activationFailed": "Activation échouée", "auth.activationFailedDescription": "Échec de l'activation de votre compte. Veuillez réessayer ou contacter le support", "auth.backToLogin": "Retour à la connexion", "auth.loginFailed": "Échec de la connexion", "auth.loginWithGoogle": "Se connecter avec Google", "auth.google": "Google", "auth.filter": "Filter", "validation.invalidEmail": "<PERSON><PERSON> invalide", "validation.passwordMinLength": "Le mot de passe doit contenir au moins 8 caractères", "validation.nameRequired": "Le nom est requis", "validation.required": "Ce champ est requis", "validation.passwordsDoNotMatch": "Les mots de passe ne correspondent pas", "validation.usernameMinLength": "Le nom d'utilisateur doit contenir au moins 2 caractères", "validation.usernameMaxLength": "Le nom d'utilisateur ne peut pas dépasser 50 caractères", "validation.usernameInvalidCharacters": "Le nom d'utilisateur ne peut contenir que des lettres, des chiffres, des traits de soulignement et des tirets", "imageSelect.pleaseSelectImageFile": "Veuillez sélectionner un fichier image", "imageSelect.selectedImage": "Image sélectionnée", "imageSelect.removeImage": "Supprimer l'image", "pixelReveal.loading": "Chargement de l'image...", "pixelReveal.processing": "Traitement de l'image...", "pixelReveal.revealComplete": "Révélation de l'image terminée", "SIGNIN_WRONG_EMAIL_PASSWORD": "Email ou mot de passe incorrect", "Try again": "<PERSON><PERSON><PERSON><PERSON>", "aiToolMenu.imagen": "Imagen", "aiToolMenu.videoGen": "Video Gen", "aiToolMenu.speechGen": "Speech Gen", "aiToolMenu.musicGen": "Music Gen", "aiToolMenu.imagen3": "Imagen 3", "aiToolMenu.imagen3Description": "Générez des images de haute qualité et détaillées avec un rendu de texte précis pour du contenu visuel créatif.", "aiToolMenu.imagen4": "Imagen 4", "aiToolMenu.imagen4Description": "Exprimez vos idées comme jamais auparavant — avec Imagen, la créativité n'a pas de limites.", "aiToolMenu.gemini2Flash": "Gemini 2.5 Flash", "aiToolMenu.gemini2FlashDescription": "Gemini 2.5 Flash est un outil puissant pour générer des images à partir de prompts texte.", "aiToolMenu.veo2": "Veo 2", "aiToolMenu.veo2Description": "Plus de contrôle, de cohérence et de créativité que jamais auparavant.", "aiToolMenu.veo3": "Veo 3", "aiToolMenu.veo3Description": "<PERSON><PERSON><PERSON><PERSON>, rencontrez l'audio. Notre dernier modèle de génération vidéo, conçu pour donner du pouvoir aux cinéastes et aux conteurs.", "aiToolMenu.gemini25Pro": "Gemini 2.5 Pro", "aiToolMenu.gemini25ProDescription": "Le modèle de synthèse vocale le plus avancé disponible.", "aiToolMenu.gemini25Flash": "Gemini 2.5 Flash", "aiToolMenu.gemini25FlashDescription": "Traitement à grande échelle (ex. multiples pdfs).\nTâches à faible latence et haut volume nécessitant de la réflexion\nCas d'usage agentiques", "aiToolMenu.link": "<PERSON><PERSON>", "aiToolMenu.linkDescription": "U<PERSON><PERSON>z NuxtLink avec des super-pouvoirs.", "aiToolMenu.soon": "Bientôt", "readArticle": "Lire l'Article", "switchToLightMode": "Passer en mode clair", "switchToDarkMode": "Passer en mode sombre", "profile": "Profil", "buyCredits.checkout": "Commande", "buyCredits.checkoutDescription": "Confirmez votre commande puis choisissez votre méthode de paiement.", "buyCredits.orderDetail": "<PERSON><PERSON><PERSON> commande", "buyCredits.credits": "Crédits", "buyCredits.pricePerUnit": "Prix par unité", "buyCredits.totalCredits": "Total des crédits", "buyCredits.totalPrice": "Prix total", "buyCredits.payment": "Paiement", "buyCredits.submit": "So<PERSON><PERSON><PERSON>", "buyCredits.cancel": "Annuler", "pricing.title": "Tarification", "pricing.description": "Choisissez le plan parfait pour vos besoins de génération d'images", "pricing.comingSoon": "Bientôt Disponible", "pricing.comingSoonDescription": "Nos plans tarifaires sont en cours de finalisation. Revenez bientôt pour des mises à jour.", "magicImageDemo.title": "Effet de Particules d'Image IA", "magicImageDemo.description": "Une démonstration du composant BaseMagicImage qui transforme les particules en images générées par IA", "magicImageDemo.image": "Image", "magicImageDemo.aboutTitle": "À Propos de Ce Composant", "magicImageDemo.aboutDescription": "Le composant BaseMagicImage utilise Three.js pour créer un système de particules qui peut se transformer entre des positions aléatoires et une image générée par IA. Les particules bougent avec des effets tourbillonnants et fluides, créant une transformation magique.", "magicImageDemo.featuresTitle": "Fonctionnalités", "magicImageDemo.features.particleRendering": "Rendu d'image basé sur les particules", "magicImageDemo.features.smoothTransitions": "Transitions fluides entre les positions aléatoires des particules et la formation d'image", "magicImageDemo.features.interactiveControls": "Contrôles de caméra interactifs (glisser pour faire tourner, défiler pour zoomer)", "magicImageDemo.features.customizable": "Nombre de particules et durée d'animation personnalisables", "magicImageDemo.features.automatic": "Déclenchement de transformation automatique ou manuel", "magicImageDemo.howItWorksTitle": "Comment ça Marche", "magicImageDemo.howItWorksDescription": "Le composant analyse les pixels d'une image et crée un système de particules 3D où chaque particule représente un pixel. Les pixels plus brillants sont positionnés plus près du spectateur, créant un effet 3D subtil. Les particules sont initialement dispersées au hasard dans l'espace 3D, puis s'animent pour former l'image lorsqu'elles sont déclenchées.", "privacy.title": "Politique de Confidentialité", "privacy.description": "Apprenez comment nous protégeons votre confidentialité et gérons vos données", "privacy.informationWeCollect": "Informations que Nous Collectons", "privacy.dataSecurity": "Sécurité des Données", "privacy.dataSecurityDescription": "Nous mettons en place des mesures de sécurité appropriées pour protéger vos informations personnelles contre l'accès non autorisé, l'altération, la divulgation ou la destruction.", "privacy.contactUs": "Contactez-Nous", "terms.title": "Conditions de Service", "terms.description": "Termes et conditions pour utiliser les services Imagen", "terms.acceptanceOfTerms": "1. Acceptation des Conditions", "terms.acceptanceOfTermsDescription": "En accédant et en utilisant les services Imagen, vous acceptez et convenez d'être lié par les termes et dispositions de cet accord.", "terms.useOfService": "2. Utilisation du Service", "terms.userAccounts": "3. <PERSON><PERSON><PERSON>", "terms.userAccountsDescription": "Vous êtes responsable de maintenir la confidentialité de votre compte et mot de passe.", "terms.intellectualProperty": "4. Propriété Intellectuelle", "terms.intellectualPropertyDescription": "Tout le contenu et les matériaux disponibles sur notre service sont protégés par des droits de propriété intellectuelle.", "terms.termination": "5. <PERSON><PERSON><PERSON><PERSON>", "terms.terminationDescription": "Nous pouvons rés<PERSON>er ou suspendre votre compte et l'accès au service à notre seule discrétion.", "terms.disclaimers": "6. Avertissements", "terms.disclaimersDescription": "Le service est fourni 'tel quel' sans aucune garantie d'aucune sorte.", "terms.contactUsTerms": "Contactez-Nous", "terms.contactUsTermsDescription": "Si vous avez des questions sur ces Conditions de Service, veuillez nous contacter via nos canaux de support.", "Describe the video you want to generate...": "Décrivez la vidéo que vous souhaitez gén<PERSON>rer...", "cancel": "Annuler", "confirm": "Confirmer", "appName": "GeminiGen.AI", "quickTopUp": "Recharge rapide", "customTopUp": "<PERSON><PERSON><PERSON>", "numberOfCredits": "Nombre de crédits", "paypal": "PayPal", "paypalDescription": "Payez en toute sécurité avec votre compte PayPal.", "stripe": "Stripe", "stripeDescription": "Payez en toute sécurité avec Stripe", "debitCreditCard": "<PERSON><PERSON> de <PERSON> ou de cré<PERSON>", "cardDescription": "Visa, Mastercard, American Express", "payWithCrypto": "Payer avec des cryptos", "cryptoDescription": "Bitcoin, Ethereum et autres cryptomonnaies", "profileMenu.guide": "Guide", "profileMenu.logo": "Logo", "profileMenu.settings": "Paramètres", "profileMenu.components": "Composants", "loadingMoreItems": "Chargement de plus d'articles...", "promptLabel": "Demande :", "videoExamples": "Exemples vidéo", "videoExamplesDescription": "Explorez ces exemples vidéo avec leurs invites et paramètres. Cliquez sur n'importe quel bouton 'Utiliser cette invite' pour copier l'invite dans votre champ de saisie.", "useThisPrompt": "Util<PERSON>z cette invite", "model": "<PERSON><PERSON><PERSON><PERSON>", "duration": "<PERSON><PERSON><PERSON>", "videoTypeSelection": "Sélectionner le type de vidéo", "notifications.title": "Notifications", "notifications.description": "Your recent notifications and updates", "notifications.totalCount": "{count} notifications", "notifications.markAllRead": "Mark all as read", "notifications.loadMore": "Load more", "notifications.close": "Close", "notifications.empty.title": "No notifications", "notifications.empty.description": "You're all caught up! No new notifications to show.", "notifications.error.title": "Error loading notifications", "notifications.types.default.title": "Notification", "notifications.types.default.description": "You have a new notification", "notifications.types.video_1.title": "Génération de vidéo en attente", "notifications.types.video_1.description": "La génération de vidéo attend d'être traitée.", "notifications.types.video_2.title": "Génération de vidéo terminée", "notifications.types.video_2.description": "La vidéo a été générée avec succès.", "notifications.types.video_3.title": "Échec de la génération de la vidéo", "notifications.types.video_3.description": "Échec de la génération vidéo", "notifications.types.image_1.title": "Génération d'images en attente", "notifications.types.image_1.description": "La génération d'image attend d'être traitée.", "notifications.types.image_2.title": "Génération d'image terminée", "notifications.types.image_2.description": "L'image a été générée avec succès.", "notifications.types.image_3.title": "Échec de la génération d'image", "notifications.types.image_3.description": "Échec de la génération d'image", "notifications.types.tts_history_1.title": "Génération audio en attente", "notifications.types.tts_history_1.description": "La synthèse vocale est en attente de traitement.", "notifications.types.tts_history_2.title": "Génération audio terminée", "notifications.types.tts_history_2.description": "La synthèse vocale a été générée avec succès.", "notifications.types.tts_history_3.title": "Échec de la génération audio", "notifications.types.tts_history_3.description": "La génération de synthèse vocale a échoué.", "notifications.types.voice_training_1.title": "Entraînement vocal en attente", "notifications.types.voice_training_1.description": "La formation vocale est en attente de traitement.", "notifications.types.voice_training_2.title": "Entraînement vocal terminé", "notifications.types.voice_training_2.description": "La formation du modèle vocal personnalisé s'est terminée avec succès.", "notifications.types.voice_training_3.title": "Échec de la formation vocale", "notifications.types.voice_training_3.description": "L'entraînement vocal a échoué.", "notifications.types.music_1.title": "Génération de Musique en Attente", "notifications.types.music_1.description": "La génération de musique attend d'être traitée.", "notifications.types.music_2.title": "Génération de musique terminée", "notifications.types.music_2.description": "La musique IA a été générée avec succès.", "notifications.types.music_3.title": "La génération de musique a échoué.", "notifications.types.music_3.description": "La génération de musique a échoué.", "notifications.types.speech_1.title": "Génération de discours en attente", "notifications.types.speech_1.description": "Votre demande de génération de discours est en attente de traitement.", "notifications.types.speech_2.title": "Génération de discours terminée", "notifications.types.speech_2.description": "Votre discours a été généré avec succès.", "notifications.types.speech_3.title": "Échec de la génération de discours", "notifications.types.speech_3.description": "La génération de votre discours a échoué. Veuillez réessayer.", "notifications.time.justNow": "À l'instant", "notifications.time.minutesAgo": "Il y a {minutes} minutes", "notifications.time.hoursAgo": "Il y a {hours}h", "notifications.time.yesterday": "<PERSON>er", "notifications.status.processing.title": "Traitement", "notifications.status.processing.description": "Votre demande est en cours de traitement.", "notifications.status.success.title": "<PERSON><PERSON><PERSON><PERSON>", "notifications.status.success.description": "<PERSON><PERSON><PERSON><PERSON> avec succès", "notifications.status.failed.title": "<PERSON><PERSON><PERSON>", "notifications.status.failed.description": "Une erreur s'est produite pendant le traitement", "notifications.status.warning.title": "Avertissement", "notifications.status.warning.description": "Terminé avec avertissements", "notifications.status.pending.title": "En attente", "notifications.status.pending.description": "En attente de traitement", "notifications.status.cancelled.title": "<PERSON><PERSON><PERSON>", "notifications.status.cancelled.description": "La demande a été annulée.", "footer.nuxtUIOnDiscord": "Nuxt UI on Discord", "profileSettings.emailNotifications": "Email Notifications", "profileSettings.marketingEmails": "Marketing Emails", "profileSettings.securityAlerts": "Security Alerts", "settings": "Paramètres", "userMenu.profile": "Profil", "userMenu.buyCredits": "Acheter des crédits", "userMenu.settings": "Paramètres", "userMenu.api": "API", "userMenu.logout": "Déconnexion", "userMenu.greeting": "Salut, {name}", "formats.mp3": "MP3", "formats.wav": "WAV", "channels.mono": "Mono", "channels.stereo": "<PERSON><PERSON><PERSON><PERSON>", "options.allow": "Autoriser", "options.dontAllow": "Ne pas autoriser", "options.voices": "Voix", "options.pickVoice": "Choisir une voix", "voiceTypes.systemVoices": "Voix système", "voiceTypes.customVoices": "Voix personnalisées", "voiceTypes.premiumVoices": "Voix premium", "voiceTypes.userVoices": "Voix utilisateur", "common.home": "Accueil", "Describe the speech you want to generate...": "Décrivez le discours que vous souhaitez générer...", "listenToSpeech": "Écouter le discours", "generateSimilar": "<PERSON><PERSON><PERSON><PERSON> simi<PERSON>", "voice": "Voix", "emotion": "Émotion", "speed": "Vitesse", "speed_settings": "Paramètres de vitesse", "speed_value": "<PERSON>ur de vitesse", "speed_slider": "Glissière de vitesse", "apply": "Appliquer", "speech_settings": "Paramètres de parole", "current_speed": "Vitesse actuelle", "reset_defaults": "Réinitialiser aux valeurs par défaut", "outputFormat": "Format de sortie", "outputChannel": "Canal de sortie", "selectVoice": "Sélectionner la voix", "selectEmotion": "Sélectionner l'émotion", "selectFormat": "Sélectionner le format", "selectChannel": "Sélectionner le canal", "noVoicesAvailable": "Aucune voix disponible", "noEmotionsAvailable": "Aucune émotion disponible", "searchVoices": "Rechercher des voix...", "searchEmotions": "Recherchez des émotions...", "noVoicesFound": "Aucune voix trouvée", "noEmotionsFound": "Aucune émotion trouvée", "retry": "<PERSON><PERSON><PERSON><PERSON>", "noAudioSample": "Aucun échantillon audio disponible", "Speech Generation Complete": "Génération de discours terminée", "Your speech has been generated successfully": "Votre discours a été généré avec succès.", "history.tabs.imagen": "Image", "history.tabs.video": "Vidéo", "history.tabs.speech": "Discours", "history.tabs.music": "Musique", "history.tabs.history": "Histoire", "orders.title": "Historique des commandes", "orders.description": "Afficher votre historique de transactions et de paiements.", "orders.orderId": "Identifiant de commande", "orders.amount": "<PERSON><PERSON>", "orders.credits": "Crédits", "orders.quantity": "Quantité", "orders.platform": "Plateforme", "orders.externalId": "Identifiant de transaction", "orders.status.completed": "<PERSON><PERSON><PERSON><PERSON>", "orders.status.success": "Su<PERSON>ès", "orders.status.paid": "<PERSON><PERSON>", "orders.status.pending": "En attente", "orders.status.processing": "Traitement", "orders.status.failed": "<PERSON><PERSON><PERSON>", "orders.status.cancelled": "<PERSON><PERSON><PERSON>", "orders.status.error": "<PERSON><PERSON><PERSON>", "orders.empty.title": "Pas encore de commandes.", "orders.empty.description": "Vous n'avez pas encore passé de commande. Achetez des crédits pour commencer à utiliser nos services.", "orders.empty.action": "Acheter des crédits", "orders.endOfList": "Vous avez vu toutes les commandes.", "orders.errors.fetchFailed": "Échec du chargement de l'historique des commandes. Veuillez réessayer.", "orders.meta.title": "Historique des commandes - Imagen AI", "orders.meta.description": "Consultez votre historique de transactions et de paiements sur Imagen AI", "historyPages.imagenDescription": "Pa<PERSON>ourez vos images et œuvres d'art générées par IA.", "historyPages.musicDescription": "Parcourez votre musique et contenu audio générés par l'IA.", "historyPages.speechDescription": "<PERSON><PERSON><PERSON>ez votre contenu discursif et vocal généré par l'IA.", "historyPages.videoDescription": "Parcourez vos vidéos et animations générées par IA.", "historyPages.imagenBreadcrumb": "Image", "historyPages.musicBreadcrumb": "Musique", "historyPages.speechBreadcrumb": "Discours", "historyPages.videoBreadcrumb": "Génération de vidéo", "historyPages.endOfImagesHistory": "Vous êtes arrivé à la fin de l'historique des images.", "historyPages.endOfMusicHistory": "Vous êtes arrivé à la fin de l'histoire de la musique.", "historyPages.endOfSpeechHistory": "Vous êtes arrivé à la fin de l'historique des discours.", "historyPages.endOfVideoHistory": "Vous êtes arrivé à la fin de l'historique des vidéos.", "historyPages.noVideosFound": "Aucune vidéo trouvée.", "historyPages.noVideosFoundDescription": "Commencez à générer des vidéos pour les voir ici.", "historyPages.backToLibrary": "Retour à la bibliothèque", "historyPages.errorLoadingVideo": "Erreur de chargement de la vidéo", "historyPages.loadingVideoDetails": "Chargement des détails de la vidéo...", "historyPages.videoDetails": "Dé<PERSON> de la vidéo", "historyPages.videoInformation": "Informations vidéo", "historyPages.videoNotFound": "La vidéo que vous cherchez n'a pas pu être trouvée ou chargée.", "historyPages.aiContentLibraryTitle": "Bibliothèque de Contenu IA", "historyPages.aiContentLibraryDescription": "<PERSON><PERSON><PERSON><PERSON> et gérez votre contenu généré par l'IA dans différentes catégories.", "demo.notifications.title": "Types de notifications et démonstration de statut", "demo.notifications.description": "Exemples de différents types de notifications avec divers états de statut", "demo.notifications.statusLegend": "Légende des statuts", "demo.speechVoiceSelect.title": "Démo de sélection de voix de synthèse", "demo.speechVoiceSelect.description": "Démonstration du composant réutilisable BaseSpeechVoiceSelectModal avec des props modelValue", "aspectRatio": "Rapport d'aspect", "Image Reference": "Référence d'image", "personGeneration.dontAllow": "Don't Allow", "personGeneration.allowAdult": "Allow Adult", "personGeneration.allowAll": "Allow All", "safety_filter_level": "Niveau de Filtrage de Sécurité", "used_credit": "<PERSON><PERSON><PERSON> utilis<PERSON>", "Safety Filter": "Filtre de sécurité", "safetyFilter.blockLowAndAbove": "Bloquer Bas et Au-dessus", "safetyFilter.blockMediumAndAbove": "Bloquer moyen et supérieur", "safetyFilter.blockOnlyHigh": "Bloc seulement en hauteur", "safetyFilter.blockNone": "<PERSON><PERSON><PERSON>", "historyFilter.all": "<PERSON>ut", "historyFilter.imagen": "Image", "historyFilter.videoGen": "Vidéo Gen", "historyFilter.speechGen": "Génération de discours", "Person Generation": "Génération de Personnes", "downloadImage": "Télécharger l'image", "noImageAvailable": "Aucune image disponible", "enhancePrompt": "Améliorer l'invite", "addImages": "Ajouter des images", "generateVideo": "Générer une vidéo", "happy": "<PERSON><PERSON><PERSON>", "sad": "Triste", "angry": "<PERSON><PERSON><PERSON>", "excited": "Excité", "laughing": "Rire", "crying": "Pleurer", "calm": "Calme", "serious": "Sérieux", "frustrated": "<PERSON><PERSON><PERSON>", "hopeful": "Optimiste", "narrative": "<PERSON><PERSON><PERSON><PERSON>", "kids' storytelling": "Conte pour enfants", "audiobook": "Livre audio", "poetic": "Poétique", "mysterious": "<PERSON>st<PERSON><PERSON>", "inspirational": "Inspirant", "surprised": "<PERSON><PERSON><PERSON>", "confident": "Confiant", "romantic": "Romantique", "scared": "<PERSON><PERSON><PERSON><PERSON>", "trailer voice": "Voix de bande-annonce", "advertising": "Publicité", "documentary": "Documentaire", "newsreader": "Présentateur de nouvelles", "weather report": "Bulletin météorologique", "game commentary": "Commentaire de jeu", "interactive": "Interactif", "customer support": "Support Client", "playful": "<PERSON><PERSON><PERSON>", "tired": "<PERSON><PERSON><PERSON>", "sarcastic": "Sarcastique", "disgusted": "Dégoûté", "whispering": "Chuchotement", "persuasive": "<PERSON><PERSON><PERSON><PERSON>", "nostalgic": "Nostalgique", "meditative": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "announcement": "<PERSON><PERSON><PERSON>", "professional pitch": "Argumentaire professionnel", "casual": "Décontracté", "exciting trailer": "Bande-annonce <PERSON>e", "dramatic": "Dramatique", "corporate": "Entreprise", "tech enthusiast": "Passionné de technologie", "youthful": "<PERSON><PERSON>", "calming reassurance": "Rassurance apaisante", "heroic": "Héroïque", "festive": "Festif", "urgent": "<PERSON><PERSON>", "motivational": "Motivationnel", "friendly": "Amical", "energetic": "Énergique", "serene": "<PERSON><PERSON>", "bold": "Audacieux", "charming": "<PERSON><PERSON><PERSON>", "monotone": "Monotone", "questioning": "Remise en question", "directive": "Directive", "dreamy": "<PERSON><PERSON><PERSON><PERSON>", "epic": "Épique", "lyrical": "Lyrical", "mystical": "Mystique", "melancholy": "Mélancolie", "cheerful": "<PERSON><PERSON>", "eerie": "Surnaturel", "flirtatious": "Flirt.", "thoughtful": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cinematic": "Cinematographique", "humorous": "Humoristique", "instructional": "Pédagogique", "conversational": "Conversationnel", "apologetic": "Désolé", "excuse-making": "Recherche d'excuses", "encouraging": "Encourageant", "neutral": "Neutre", "authoritative": "Autoritaire", "sarcastic cheerful": "Sarcastique enjoué", "reassuring": "<PERSON><PERSON><PERSON><PERSON>", "formal": "Formel", "anguished": "<PERSON><PERSON><PERSON><PERSON>", "giggling": "Ricanement", "exaggerated": "Exagé<PERSON>", "cold": "Froid", "hot-tempered": "Colérique", "grateful": "Reconnaissant", "regretful": "Regrettable", "provocative": "Provocant", "triumphant": "Triomphant", "vengeful": "<PERSON><PERSON><PERSON>", "heroic narration": "Narration héro<PERSON>", "villainous": "<PERSON><PERSON><PERSON>", "hypnotic": "Hypnotique", "desperate": "Désespéré", "lamenting": "Se lamenter", "celebratory": "Célébration", "teasing": "Taquinerie", "exhausted": "<PERSON><PERSON><PERSON><PERSON>", "questioning suspicious": "Remise en question des suspicions", "optimistic": "Optimiste", "bright, gentle voice, expressing excitement.": "Voix claire et douce, exprimant de l'enthousiasme.", "low, slow voice, conveying deep emotions.": "Voix basse et lente, exprimant des émotions profondes.", "sharp, exaggerated voice, expressing frustration.": "Voix aiguë et exagé<PERSON>e, exprimant de la frustration.", "fast, lively voice, full of enthusiasm.": "Voix rapide et vive, pleine d'enthousiasme.", "interrupted, joyful voice, interspersed with laughter.": "Voix interrompue et joyeuse, entrecoupée de rires.", "shaky, low voice, expressing pain.": "Voix tremblante et basse, exprimant la douleur.", "gentle, steady voice, providing reassurance.": "Voix douce et posée, offrant de l'assurance.", "mature, clear voice, suitable for formal content.": "Voix mature et claire, adaptée au contenu formel.", "weary, slightly irritated voice.": "Voix fatiguée, légèrement irritée.", "bright voice, conveying positivity and hope.": "Voix lumineuse, transmettant positivité et espoir.", "natural, gentle voice with a slow rhythm.": "Voix naturelle et douce avec un rythme lent.", "lively, engaging voice, captivating for children.": "Voix vive et engageante, captivante pour les enfants.", "even, slow voice, emphasizing content meaning.": "Voix posée et lente, mettant l'accent sur le sens du contenu.", "rhythmic, emotional voice, conveying subtlety.": "Voix rythmique et émotive, transmettant de la subtilité.", "low, slow voice, evoking curiosity.": "Voix basse et lente, éveillant la curiosité.", "strong, passionate voice, driving action.": "Voix forte et passionnée, incitant à l'action.", "high, interrupted voice, expressing astonishment.": "Voix haute et interrompue, exprimant l'étonnement.", "firm, powerful voice, persuasive and assuring.": "Voix ferme, puissante, persuasive et rassurante.", "sweet, gentle voice, suitable for emotional content.": "Voix douce et suave, adaptée aux contenus émotionnels.", "shaky, interrupted voice, conveying anxiety.": "<PERSON>oi<PERSON> tremblante, entre<PERSON><PERSON><PERSON>, exprimant de l'anxiété.", "deep, strong voice with emphasis, creating suspense.": "Voix profonde et forte avec emphase, créant du suspense.", "engaging, lively voice, emphasizing product benefits.": "Voix engageante et vivante, mettant en avant les avantages du produit.", "formal, clear voice with focus on key points.": "Voix formelle et claire avec un accent sur les points clés.", "calm, profound voice, delivering authenticity.": "Voix calme et profonde, délivrant de l'authenticité.", "standard, neutral voice, clear and precise.": "Voix standard, neutre, claire et précise.", "bright, neutral voice, suitable for concise updates.": "Voix claire et neutre, adaptée aux mises à jour concises.", "fast, lively voice, stimulating excitement.": "Voix rapide et vivante, stimulant l'excitation.", "friendly, approachable voice, encouraging engagement.": "Voix amicale et accessible, encourageant l'engagement.", "empathetic, gentle voice, easy to connect with.": "Voix empathique et douce, facile à connecter.", "clear voice, emphasizing questions and answers.": "<PERSON>oix claire, en mettant l'accent sur les questions et les réponses.", "cheerful, playful voice with a hint of mischief.": "Voix joyeuse et enjouée avec une pointe de malice.", "slow, soft voice lacking energy.": "Voix lente et douce manquant d'énergie.", "ironic, sharp voice, sometimes humorous.": "<PERSON><PERSON>, voix aiguisée, parfois humoristique.", "cold voice, clearly expressing discomfort.": "<PERSON>oix froide, exprimant clairement un malaise.", "soft, mysterious voice, creating intimacy.": "Voix douce et mystérieuse, créant de l'intimité.", "emotional voice, convincing the listener to act.": "Voix émotive, convaincant l'auditeur d'agir.", "gentle voice, evoking feelings of reminiscence.": "<PERSON><PERSON><PERSON> do<PERSON>, évoquant des sentiments de réminiscence.", "even, relaxing voice, suitable for mindfulness.": "Voix même, relaxante, adaptée à la pleine conscience.", "clear voice, emphasizing key words.": "<PERSON>oix claire, en mettant l'accent sur les mots clés.", "confident, clear voice, ideal for business presentations.": "Voix confiante et claire, idéale pour les présentations professionnelles.", "natural, friendly voice, as if talking to a friend.": "Voix naturelle et amicale, comme si l'on parlait à un ami.", "fast, powerful voice, creating tension and excitement.": "Voix rapide et puissante, créant tension et excitation.", "emphasized, suspenseful voice, creating intensity.": "Voix accentuée et pleine de suspense, créant de l'intensité.", "professional, formal voice, suitable for business content.": "Voix professionnelle et formelle, adaptée au contenu commercial.", "energetic, lively voice, introducing new technologies.": "Voix énergique et vivante, introduisant de nouvelles technologies.", "vibrant, cheerful voice, appealing to younger audiences.": "Voix vibrante et joyeuse, qui attire un public plus jeune.", "gentle, empathetic voice, easing concerns.": "Voix douce et empathique, apaisant les inquiétudes.", "strong, decisive voice, full of inspiration.": "Voix forte et décisive, pleine d'inspiration.", "bright, excited voice, suitable for celebrations.": "Voix brillante et enthousiaste, adaptée aux célébrations.", "fast, strong voice, emphasizing urgency.": "Voix rapide et forte, accentuant l'urgence.", "passionate, inspiring voice, encouraging action.": "Voix passionnée et inspirante, encourageant l'action.", "warm, approachable voice, fostering connection.": "Voix chaleureuse et accueillante, favorisant la connexion.", "fast, powerful voice, brimming with enthusiasm.": "Voix rapide et puissante, débordante d'enthousiasme.", "slow, gentle voice, evoking peace and tranquility.": "Voix lente et douce, évoquant paix et tranquillité.", "firm, assertive voice, exuding confidence.": "Voix ferme et assurée, dégageant de la confiance.", "warm, captivating voice, leaving a strong impression.": "Voix chaleureuse et captivante, laissant une forte impression.", "flat, unvaried voice, conveying neutrality or irony.": "Voix plate et uniforme, exprimant la neutralité ou l'ironie.", "curious voice, emphasizing questions.": "Voix curieuse, accentuant les questions.", "firm, clear voice, guiding the listener step-by-step.": "Voix ferme et claire, guidant l'auditeur pas à pas.", "gentle, slow voice, evoking a floating sensation.": "Voix douce et lente, évoquant une sensation de flottement.", "deep, resonant voice, emphasizing grandeur.": "Voix profonde et résonnante, soulignant la grandeur.", "soft, melodic voice, similar to singing.": "Voix douce et mélodieuse, semblable à du chant.", "low, drawn-out voice, evoking mystery.": "Voix basse et prolongée, évoquant le mystère.", "slow, low voice, conveying deep sadness.": "Voix lente et basse, exprimant une profonde tristesse.", "bright, energetic voice, full of positivity.": "Voix lumineuse et énergique, pleine de positivité.", "low, whispery voice, evoking fear or strangeness.": "Voix basse et chuchotante, évoquant la peur ou l'étrangeté.", "sweet, teasing voice, full of allure.": "Voix douce et taquine, pleine de charme.", "slow, reflective voice, full of contemplation.": "Voix lente et réfléchie, pleine de contemplation.", "resonant, emphasized voice, creating a movie-like effect.": "Voix résonnante et accentuée, créant un effet cinématographique.", "lighthearted, cheerful voice, sometimes exaggerated.": "Voix enjouée et joyeuse, parfois exag<PERSON>.", "clear, slow voice, guiding the listener step-by-step.": "Voix claire et lente, guidant l'auditeur étape par étape.", "natural voice, as if chatting with the listener.": "Voix naturelle, comme si l'on discutait avec l'auditeur.", "soft, sincere voice, expressing regret.": "Voix douce et sincère, exprimant des regrets.", "hesitant, uncertain voice, sometimes awkward.": "Voix hésitante, incertaine, parfois maladroite.", "warm voice, providing motivation and support.": "Voix chaleureuse, offrant motivation et soutien.", "even voice, free of emotional bias.": "Voix neutre, sans biais émotionnel.", "strong, powerful voice, exuding credibility.": "Voix forte et puissante, dégageant de la crédibilité.", "cheerful voice with an undertone of mockery.": "Voix enjouée avec un sous-entendu de moquerie.", "gentle, empathetic voice, providing comfort.": "Voix douce et empathique, apportant du réconfort.", "clear, polite voice, suited for formal occasions.": "Voix claire et polie, adaptée aux occasions formelles.", "urgent, shaky voice, expressing distress.": "<PERSON><PERSON>, voix tremblante, exprimant la d<PERSON>tresse.", "interrupted voice, mixed with light laughter.": "Voix interrompue, mêlée de légers rires.", "loud, emphasized voice, often humorous.": "Voix forte et accentuée, souvent humoristique.", "flat, unemotional voice, conveying detachment.": "Voix plate et dépourvue d'émotion, transmettant le détachement.", "fast, sharp voice, sometimes out of control.": "Voix rapide et aiguë, parfois incontrôlable.", "warm, sincere voice, expressing appreciation.": "Voix chaude et sincère, exprimant de la reconnaissance.", "low, subdued voice, full of remorse.": "Voix basse et feutrée, pleine de remords.", "challenging, strong voice, full of insinuation.": "Exigeant, voix forte, pleine d'insinuations.", "loud, powerful voice, full of victory.": "Voix forte et puissante, pleine de victoire.", "low, cold voice, expressing determination for revenge.": "Voix basse et froide, exprimant une détermination à se venger.", "strong, inspiring voice, emphasizing heroic deeds.": "Voix forte et inspirante, soulignant les actes héroïques.", "low, drawn-out voice, full of scheming.": "Voix basse et traînante, pleine de manigances.", "even, repetitive voice, drawing the listener in.": "Voix égale et répétitive, captivant l'auditeur.", "urgent, shaky voice, expressing hopelessness.": "<PERSON><PERSON>, voix tremblante, exprimant le désespoir.", "low, sorrowful voice, as if mourning.": "Voix basse et plaintive, comme un deuil.", "excited, joyful voice, full of festive spirit.": "Voix excitée, joyeuse, pleine d'esprit festif.", "light, playful voice, sometimes mockingly.": "Voix légère et enjouée, parfois moqueuse.", "weak, broken voice, expressing extreme fatigue.": "Voix faible et brisée, exprimant une fatigue extrême.", "slow, emphasized voice, full of suspicion.": "<PERSON>oix lente, appuy<PERSON>, pleine de suspicion.", "bright, hopeful voice, creating positivity.": "Voix lumineuse et pleine d'espoir, créant de la positivité.", "Your audio will be processed with the latest stable model.": "Votre audio sera traité avec le dernier modèle stable.", "Your audio will be processed with the latest beta model.": "Votre audio sera traité avec le dernier modèle bêta.", "You don't have any saved prompts yet.": "Vous n'avez pas encore de invites enregistrées.", "Commercial Use": "Usage commercial", "Other people’s privacy": "La vie privée des autres", "You must respect the privacy of others when using our services. Do not upload or create speech output containing personal information, confidential data, or copyrighted material without permission.": "Vous devez respecter la vie privée des autres lors de l'utilisation de nos services. Ne téléchargez pas et ne créez pas de sorties vocales contenant des informations personnelles, des données confidentielles ou du matériel protégé par des droits d'auteur sans permission.", "{price}$ per credit": "{price}$ par crédit", "Pricing": "Tarification", "Simple and flexible. Only pay for what you use.": "Simple et flexible. Payez uniquement ce que vous consommez.", "Pay as you go": "Paiement à l'utilisation", "Flexible": "Flexible", "Input characters": "Caractères d'entrée", "Audio model": "Modèle audio", "Credits": "Crédits", "Cost": "Coût", "HD quality voices": "Voix de qualité HD", "Advanced model": "<PERSON><PERSON><PERSON><PERSON>", "Buy now": "Achetez maintenant", "Paste your text to calculate": "Collez votre texte pour calculer", "Paste your text here...": "Collez votre texte ici...", "Calculate": "Calculer", "Estimate your cost by drag the slider below or": "Estimez votre coût en faisant glisser le curseur ci-dessous ou", "calming": "A<PERSON><PERSON>t", "customer": "Client", "exciting": "passionnant", "excuse": "Excuse-moi", "game": "<PERSON><PERSON>", "hot": "<PERSON><PERSON>", "kids": "<PERSON><PERSON><PERSON>", "professional": "Professionnel", "tech": "Technologie", "trailer": "Remorque", "weather": "Temps", "No thumbnail available": "Miniature non disponible", "Debit or Credit Card": "<PERSON><PERSON> de <PERSON> ou de cré<PERSON>", "Visa, Mastercard, American Express and more": "Visa, Mastercard, American Express et plus encore", "Top up now": "Rechargez maintenant", "noVideoAvailable": "Aucune vidéo disponible", "common.back": "Retour", "common.edit": "Modifier", "common.save": "<PERSON><PERSON><PERSON><PERSON>", "common.cancel": "Annuler", "common.delete": "<PERSON><PERSON><PERSON><PERSON>", "common.copy": "<PERSON><PERSON><PERSON>", "common.copied": "<PERSON><PERSON><PERSON>", "common.manage": "<PERSON><PERSON><PERSON>", "aiToolMenu.textToImage": "Texte en image", "profileMenu.integration": "Intégration", "videoTypes.examples.tikTokDanceTrend": "Tendance de danse TikTok", "videoTypes.examples.energeticDanceDescription": "Vidéo de danse énergique avec des couleurs vives, des coupures rapides, de la musique tendance, format vertical, style réseau social", "videoTypes.examples.instagramReel": "<PERSON><PERSON>", "videoTypes.examples.lifestyleDescription": "Contenu de style de vie avec des visuels esthétiques, des transitions fluides, des effets tendance, un récit captivant", "videoTypes.examples.comedySketch": "Sketch comique", "videoTypes.examples.funnyComedyDescription": "Scène comique amusante avec des personnages expressifs, des situations humoristiques, des dialogues divertissants, ambiance légère.", "videoTypes.examples.productLaunchAd": "Annonce de lancement de produit", "videoTypes.examples.professionalCorporateDescription": "Vidéo professionnelle d'entreprise avec présentation exécutive, environnement de bureau propre, style formel d'affaires", "videoTypes.examples.quickPromoVideo": "Vidéo promo rapide", "videoTypes.examples.fastPacedPromoDescription": "Contenu promotionnel dynamique avec une production efficace, des visuels économiques, un message simplifié.", "videoTypes.examples.birthdayGreeting": "Salutation d'anniversaire", "videoTypes.examples.personalizedBirthdayDescription": "Vidéo d'anniversaire personnalisée avec des décorations festives, un éclairage chaleureux, une ambiance de fête, un message sincère.", "videoTypes.examples.brandStoryVideo": "Vidéo de l'histoire de la marque", "videoTypes.examples.tutorialVideo": "<PERSON><PERSON><PERSON><PERSON>", "videoTypes.examples.manOnThePhone": "Homme au téléphone", "videoTypes.examples.runningSnowLeopard": "Course du léopard des neiges", "videoTypes.examples.snowLeopard": "Léopard des neiges", "videoTypes.styles.cartoonAnimated": "Vidéo de style dessin animé ou animé", "videoTypes.styles.naturalDocumentary": "Séquences de type documentaire naturel", "videoTypes.styles.naturalLifelike": "Style vidéo naturel et réaliste", "videoTypes.styles.professionalMovieQuality": "Qualité professionnelle digne d'un film avec un éclairage dramatique", "videoTypes.styles.creativeStylized": "Effets vidéo créatifs et stylisés", "videoTypes.styles.retroVintage": "Esthétique vidéo r<PERSON>tro ou vintage", "historyFilter.dialogueGen": "Dialogue Gen", "historyFilter.speechGenDocument": "Génération de discours à partir du document", "demo.notifications.availableNotificationTypes": "Types de notifications disponibles", "demo.speechVoiceSelect.example1": "Exemple 1 : Utilisation par défaut", "demo.speechVoiceSelect.example2": "Exemple 2 : <PERSON><PERSON> <PERSON><PERSON>", "demo.speechVoiceSelect.example3": "Exemple 3 : <PERSON> taille", "demo.speechVoiceSelect.example4": "Exemple 4 : Plusieurs exemples d'erreurs", "demo.speechVoiceSelect.example5": "Exemple 5 : Comparaison des statuts", "demo.speechVoiceSelect.mainNarrator": "Narrateur principal", "demo.speechVoiceSelect.characterVoice": "Voix de personnage", "demo.speechVoiceSelect.selectedVoicesSummary": "Résumé des voix sélectionnées :", "demo.speechVoiceSelect.clearAll": "Tout effacer", "demo.speechVoiceSelect.setRandomVoices": "Définir des Voix Aléatoires", "demo.speechVoiceSelect.logToConsole": "Journaliser dans la console", "demo.speechVoiceSelect.notSelected": "Non sélectionné", "demo.speechVoiceSelect.voiceSelectionsChanged": "Sélections vocales modifiées", "demo.historyWrapper.title": "Démo du Badge d'État de HistoryWrapper", "demo.historyWrapper.normalStatus": "Exemple 1 : Statut Normal (statut = 1)", "demo.historyWrapper.processingStatus": "Exemple 2 : Traitement en cours (statut = 2)", "demo.historyWrapper.errorStatus": "Exemple 3 : <PERSON><PERSON> d'erreur (statut = 3) - Affiche le badge d'erreur", "demo.historyWrapper.multipleErrorExamples": "Exemple 4 : Multiples exemples d'erreurs", "demo.historyWrapper.statusComparison": "Exemple 5 : Comparaison de l'état", "demo.historyWrapper.normalImageGeneration": "Génération d'images normale", "demo.historyWrapper.videoGenerationInProgress": "Génération de vidéo en cours", "demo.historyWrapper.speechGenerationFailed": "Échec de la génération de discours", "demo.historyWrapper.imageFailed": "Image échouée", "demo.historyWrapper.videoFailed": "Échec de la vidéo", "demo.historyWrapper.speechFailed": "Discours échoué", "demo.historyWrapper.statusSuccess": "Statut : Succès", "demo.historyWrapper.statusProcessing": "Statut : Traitement", "demo.historyWrapper.statusError": "État : <PERSON><PERSON><PERSON>", "demo.historyWrapper.status1Success": "Statut 1 : Succès", "demo.historyWrapper.status2Processing": "Statut 2 : En cours de traitement", "demo.historyWrapper.badgeBehavior": "Comportement des badges :", "demo.historyWrapper.showsOnlyTypeAndStyle": "Montre uniquement les badges de type et de style", "demo.historyWrapper.showsTypeStyleAndError": "<PERSON>re le type, le style, et l'insigne d'erreur rouge avec une icône d'alerte.", "demo.historyWrapper.redBackgroundWithWhite": "Fond rouge avec texte blanc et icône de cercle d'alerte", "demo.historyWrapper.allBadgesHideOnHover": "Tous les badges se masquent au survol pour afficher le contenu de la superposition.", "demo.speechVoiceCaching.title": "Test de mise en cache de la voix parlée", "demo.speechVoiceCaching.description": "Test pour vérifier la mise en cache des voix entre différents composants.", "demo.speechVoiceCaching.component1Modal": "Composant 1 - Modal", "demo.speechVoiceCaching.component3RegularSelect": "Composant 3 - Sélection régulière", "demo.speechVoiceCaching.forceReloadVoices": "Recharger les voix de force", "demo.speechVoiceCaching.clearAllSelections": "Effacer toutes les sélections", "demo.speechVoiceCaching.logStoreState": "État du magasin de journaux", "demo.speechVoiceCaching.refreshPageInstructions": "Actualisez la page et ouvrez n'importe quel composant - cela se rechargera.", "demo.speechVoiceCaching.checkNetworkTab": "Vérifiez l'onglet Réseau pour confirmer les appels API", "demo.speechVoiceCaching.selectedVoicePersist": "La voix sélectionnée sera conservée via le localStorage.", "demo.speechVoiceCaching.pageMounted": "<PERSON> mon<PERSON>, le magasin s'initialisera automatiquement si nécessaire.", "integration.title": "Intégration", "integration.subtitle": "<PERSON><PERSON><PERSON> vos clés d'API et paramètres d'intégration.", "integration.apiKeys": "Clés API", "integration.apiKeysDescription": "G<PERSON>rez vos clés API pour un accès programmatique.", "integration.webhook": "Webhook", "integration.webhookDescription": "Configurer l'URL du webhook pour les notifications", "apiKeys.title": "Clés API", "apiKeys.subtitle": "G<PERSON>rez vos clés API pour un accès programmatique.", "apiKeys.create": "<PERSON><PERSON><PERSON> une clé API", "apiKeys.createNew": "<PERSON><PERSON>er une nouvelle clé API", "apiKeys.createFirst": "<PERSON><PERSON>er une première clé API", "apiKeys.name": "Nom", "apiKeys.nameDescription": "Donnez à votre clé API un nom descriptif.", "apiKeys.namePlaceholder": "par exemple, clé API de mon application", "apiKeys.nameRequired": "Le nom de la clé API est requis.", "apiKeys.createdAt": "<PERSON><PERSON><PERSON>", "apiKeys.noKeys": "Pas de clés API", "apiKeys.noKeysDescription": "Créez votre première clé API pour commencer à accéder de manière programmatique.", "apiKeys.created": "Clé API créée avec succès", "apiKeys.createError": "Échec de la création de la clé API", "apiKeys.deleted": "Clé API supprimée avec succès", "apiKeys.deleteError": "Échec de la suppression de la clé API", "apiKeys.deleteConfirm": "Supprimer la clé API", "apiKeys.deleteWarning": "Êtes-vous sûr de vouloir supprimer cette clé API ? Cette action est irréversible.", "apiKeys.copied": "Clé API copiée dans le presse-papiers", "apiKeys.copyError": "Échec de la copie de la clé API", "webhook.title": "Configuration de Webhook", "webhook.subtitle": "Configurer l'URL du webhook pour les notifications en temps réel", "webhook.configuration": "URL de webhook", "webhook.currentUrl": "URL de Webhook actuel", "webhook.currentUrlDescription": "Cette URL recevra des requêtes POST pour les événements webhook.", "webhook.notConfigured": "Aucune URL de webhook configurée", "webhook.url": "URL du Webhook", "webhook.urlDescription": "Entrez l'URL où vous souhaitez recevoir les notifications de webhook.", "webhook.urlPlaceholder": "https://your-domain.com/webhook", "webhook.urlRequired": "Veuillez d'abord entrer une URL de webhook.", "webhook.invalidUrl": "Veuillez saisir une URL valide.", "webhook.saved": "URL du webhook enregistré avec succès.", "webhook.saveError": "Échec de l'enregistrement de l'URL du webhook.", "webhook.test": "Test", "webhook.testSent": "Message de test", "webhook.testDescription": "Test du webhook envoy<PERSON> avec succès.", "webhook.information": "Informations sur le webhook", "webhook.howItWorks": "Comment ça fonctionne", "webhook.description": "Lorsqu'il est configuré, nous enverrons des requêtes HTTP POST à votre URL de webhook chaque fois que certains événements se produisent dans votre compte.", "webhook.events": "Événements Webhook", "webhook.imageGenerated": "Génération d'image terminée", "webhook.imageGenerationFailed": "La génération d'image a échoué.", "webhook.creditUpdated": "Solde du crédit mis à jour", "webhook.payloadFormat": "Format de charge utile", "webhook.payloadDescription": "Les requêtes Webhook seront envoyées sous forme de JSON avec la structure suivante :", "webhook.security": "Sécurité", "webhook.securityDescription": "Nous recommandons d'utiliser des URL HTTPS et de mettre en œuvre la vérification des signatures pour garantir l'authenticité des webhooks.", "error.general": "<PERSON><PERSON><PERSON>", "error.validation": "Erreur de validation", "error.required": "Champ obligatoire", "success.saved": "Enregistré avec succès", "success.created": "<PERSON><PERSON><PERSON> avec succès", "success.deleted": "Supprimé avec succès", "success.copied": "Copié dans le presse-papiers", "confirmDelete": "Confirmer la <PERSON>", "confirmDeleteDescription": "Êtes-vous sûr de vouloir supprimer cet article ? Cette action est irréversible.", "historyDeleted": "Élément d'historique supprimé avec succès.", "deleteError": "Échec de la suppression de l'élément historique", "Regenerate Image": "Régénérer l'image", "You haven't made any changes to the settings. Are you sure you want to regenerate the same image?": "Vous n'avez apporté aucune modification aux paramètres. Êtes-vous sûr de vouloir régénérer la même image ?", "Yes, Regenerate": "<PERSON><PERSON>, Régénérer", "Cancel": "Annuler", "models.imagen4Fast": "Imagen 4 Rapide", "models.imagen4Ultra": "Imagen 4 Ultra", "voiceTypes.favoriteVoices": "Voix préférées", "voiceTypes.geminiVoices": "Voix des Gémeaux", "speech.dialogueGeneration.complete": "Génération de dialogue terminée", "speech.dialogueGeneration.failed": "Échec de la génération du dialogue", "speech.dialogueGeneration.pending": "Génération de Dialogue en Attente", "speech.dialogueGeneration.dialogueGen": "Génération de dialogue", "speech.dialogueGeneration.successMessage": "Votre dialogue a été généré avec succès.", "speech.speechGeneration.complete": "Génération de discours terminée", "speech.speechGeneration.failed": "Échec de la génération de discours", "speech.speechGeneration.pending": "Génération de discours en attente", "speech.speechGeneration.successMessage": "Votre discours a été généré avec succès.", "speech.speechGeneration.requestWaiting": "Votre demande de génération de discours est en attente de traitement.", "speech.errors.failedToLoadEmotions": "Échec du chargement des émotions", "tts-document": "Fichier à la parole", "assignVoicesToSpeakers": "Attribuer des voix aux interlocuteurs", "speakers": "Haut-parleurs", "addSpeaker": "Ajouter un intervenant", "noVoiceAssigned": "Aucune voix assignée", "noSpeakersAdded": "Aucun intervenant ajouté pour le moment", "assignVoiceToSpeaker": "Attribuer une voix à {speaker}", "assigned": "Attribué", "assign": "Attribuer", "editSpeaker": "Modifier le locuteur", "speakerName": "Nom du conférencier", "enterSpeakerName": "Entrez le nom de l'orateur", "save": "<PERSON><PERSON><PERSON><PERSON>", "speaker": "Conférencier", "assignVoices": "Attribuer des voix", "speakersWithVoices": "{assigned}/{total} orateurs ont des voix", "dialogs": "Dialogues", "addDialog": "Ajouter une boîte de dialogue", "enterDialogText": "Entrez le texte du dialogue...", "selectSpeaker": "Sé<PERSON><PERSON><PERSON> le haut-parleur", "generateDialogSpeech": "Générer un discours dialogué", "voice 1": "Voix 1", "voice 2": "Voix 2", "uuid": "UUID", "output_format": "Format de sortie", "output_channel": "Canal de sortie", "file_name": "Nom de fi<PERSON>er", "file_size": "<PERSON><PERSON>", "speakers_count": "Nombre de haut-parleurs", "custom_prompt": "<PERSON><PERSON><PERSON>", "Please wait a moment...": "Veuillez patienter un instant...", "Click to copy": "Cliquer pour copier", "Copied to clipboard": "Copié dans le presse-papiers", "UUID has been copied to clipboard": "L'UUID a été copié dans le presse-papiers.", "Credits: {credits} remaining": "Crédits : {credits} restants", "This generation will cost: {cost} Credits": "Cette génération coûtera : {cost} Crédits", "This generation will cost: {cost} Credits for {duration}s": "Cette génération coûtera : {cost} Crédits pour {duration} secondes", "Your generated video will appear here": "Votre vidéo générée apparaîtra ici.", "Regenerate Video": "Régénérer la vidéo", "You haven't made any changes to the settings. Are you sure you want to regenerate the same video?": "Vous n'avez apporté aucune modification aux paramètres. Êtes-vous sûr de vouloir régénérer la même vidéo ?", "Your generated speech will appear here": "Votre discours généré apparaîtra ici.", "Regenerate Speech": "Régénérer le discours", "You haven't made any changes to the settings. Are you sure you want to regenerate the same speech?": "Vous n'avez apporté aucun changement aux paramètres. Êtes-vous sûr de vouloir régénérer le même discours ?", "Generated Speech": "Discours généré", "Generating speech...": "Génération de la parole...", "View Details": "Voir les détails", "Speech Examples": "Exemples de discours", "Click on any example to use its prompt for speech generation": "Cliquez sur n'importe quel exemple pour utiliser son invite pour la génération de discours.", "Click to use": "Cliquez pour utiliser", "videoStyles.selectVideoStyle": "Sélectionner le style vidéo", "videoStyles.cinematic": "Cinematic", "videoStyles.realistic": "<PERSON><PERSON><PERSON><PERSON>", "videoStyles.animated": "<PERSON><PERSON><PERSON>", "videoStyles.artistic": "Artistique", "videoStyles.documentary": "Documentaire", "videoStyles.vintage": "<PERSON><PERSON><PERSON>", "ui.buttons.downloadApp": "Télécharger l'application", "ui.buttons.signUp": "S'inscrire", "ui.buttons.viewDetails": "Voir les détails", "ui.buttons.seeLater": "À plus tard", "ui.buttons.selectFile": "Sélectionner un fichier", "ui.buttons.selectFiles": "Sélectionner des fichiers", "ui.buttons.pickAVoice": "Choisissez une voix", "ui.buttons.topUpNow": "Rechargez maintenant", "ui.buttons.pressEscToClose": "Appuyez sur ÉCHAP pour fermer", "ui.labels.clickToCopy": "Cliquez pour copier", "ui.labels.copiedToClipboard": "Copié dans le presse-papiers", "ui.labels.noAudioAvailable": "Aucun audio disponible", "ui.labels.noThumbnailAvailable": "Aucune miniature disponible", "ui.labels.noPromptAvailable": "Aucune invite disponible.", "ui.labels.videoModel": "<PERSON><PERSON><PERSON><PERSON> v<PERSON>", "ui.labels.speechModel": "<PERSON><PERSON><PERSON><PERSON> de discours", "ui.labels.generatedSpeech": "Discours généré", "ui.labels.defaultVoice": "Voix par défaut", "ui.labels.selectAnyVoice": "Sélectionnez une voix", "ui.labels.cameraMotion": "Mouvement de caméra", "ui.labels.transform": "Transformer", "ui.labels.transforming": "Transformation...", "ui.messages.imageLoaded": "Image chargée", "ui.messages.imageRevealComplete": "Image révélée intégralement.", "ui.messages.processingImage": "Traitement de l'image", "ui.messages.videoLoaded": "<PERSON><PERSON><PERSON><PERSON> char<PERSON>", "ui.messages.videoProcessing": "Traitement vidéo", "ui.messages.invalidDownloadLink": "Lien de téléchargement invalide", "ui.messages.pleaseSelectSupportedFile": "Veuillez sélectionner un fichier pris en charge.", "ui.messages.deleteConfirm": "Confirmer la <PERSON>", "ui.messages.deleteFailed": "Suppression échouée", "ui.messages.youHaveNewNotification": "Vous avez une nouvelle notification.", "ui.messages.yourGenerationIsReady": "Votre génération est prête", "ui.errors.errorLoadingImage": "Erreur de chargement de l'image :", "ui.errors.failedToCopy": "Échec de la copie :", "ui.errors.failedToPlayAudioPreview": "Échec de la lecture de l'aperçu audio :", "ui.errors.wavesurferError": "<PERSON><PERSON><PERSON> :", "ui.errors.somethingWentWrong": "<PERSON><PERSON><PERSON> chose s'est mal passé. Veuillez réessayer.", "ui.errors.supabaseUrlRequired": "L'URL de Supabase et la clé anonyme sont nécessaires.", "dialog.startTypingHere": "Commencez à taper le dialogue ici...", "payment.debitCreditCard": "<PERSON><PERSON> de <PERSON> ou de cré<PERSON>", "payment.cardDescription": "Visa, Mastercard, American Express et plus encore", "Style Description": "Description du style", "Dialog Content": "Contenu du dialogue", "Your generated dialog will appear here": "Votre dialogue généré apparaîtra ici.", "Regenerate Dialog": "R<PERSON><PERSON>n<PERSON><PERSON> le dialogue", "Generated Dialog": "<PERSON> gén<PERSON>", "Generating dialog...": "Génération de dialogue...", "Dialog Information": "Informations de dialogue", "Audio Player": "Lecteur audio", "Voices": "Voix", "Voice 1": "Voix 1", "Voice 2": "Voix 2", "Dialog Examples": "Exemples de dialogue", "Click on any example to use its style or dialog content": "Cliquez sur un exemple pour utiliser son style ou son contenu de dialogue.", "Use Style": "Utiliser le style", "Use Dialog": "Utiliser le dialogue", "personGeneration": "Génération de personnes", "Imagen": "Image", "On": "On", "Off": "Désactivé", "Prompts will always be refined to improve output quality": "<PERSON> invites seront toujours affinées pour améliorer la qualité des résultats.", "Prompts will not be modified": "Les invites ne seront pas modifiées.", "Tips": "Conseils", "Your video is still being generated in the background. You can close this page and check the history tab for the generated video and we will notify you when it is ready.": "Votre vidéo est toujours en cours de génération en arrière-plan. Vous pouvez fermer cette page et vérifier l'onglet historique pour la vidéo générée et nous vous informerons lorsqu'elle sera prête.", "Go to History": "Aller à l'Histoire", "footer.youtube": "Youtube", "footer.doctransGPT": "DoctransGPT", "footer.textToSpeechOpenAI": "Texte en parole OpenAI", "footer.privacyPolicy": "Politique de confidentialité", "footer.termsOfService": "Conditions d'utilisation", "footer.terms": "Conditions", "footer.privacy": "Confidentialité", "Generate": "<PERSON><PERSON><PERSON><PERSON>", "Prompt": "Invite", "Generate Video": "Générer une vidéo", "ui.errors.generationFailed": "Échec de génération", "downloadVideo": "Télécharger la vidéo", "imageStyles.selectImageStyle": "Sélect<PERSON>ner le style d'image", "imageStyles.none.description": "Aucun style spécifique appliqué", "imageStyles.3d-render.description": "Rendre l'image en 3D", "imageStyles.acrylic.description": "C<PERSON>er une image dans le style de la peinture acrylique", "imageStyles.anime-general.description": "Générer une image dans le style anime", "imageStyles.creative.description": "Appliquer des effets artistiques créatifs", "imageStyles.dynamic.description": "Créez des visuels dynamiques et énergiques.", "imageStyles.fashion.description": "Image de style pour la photographie de mode", "imageStyles.game-concept.description": "Conception d'image pour l'art conceptuel de jeu", "imageStyles.graphic-design-3d.description": "Appliquer des éléments de conception graphique en 3D", "imageStyles.illustration.description": "Créer des illustrations artistiques", "imageStyles.portrait.description": "Optimiser pour la photographie portrait", "imageStyles.portrait-cinematic.description": "C<PERSON>er un style de portrait cinématographique", "imageStyles.portrait-fashion.description": "Appliquer le style portrait de mode", "imageStyles.ray-traced.description": "Rendu avec des effets de lancer de rayons", "imageStyles.stock-photo.description": "Créer un style de photo de stock professionnel", "imageStyles.watercolor.description": "Appliquer des effets de peinture à l'aquarelle", "imageStyles.examples": "Exemples", "ui.messages.dragDropOrClick": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>z les fichiers ici ou cliquez pour sélectionner.", "ui.messages.dropFilesHere": "Déposez les fichiers ici", "ui.messages.selectMultipleFiles": "Vous pouvez sélectionner plusieurs fichiers.", "ui.messages.selectSingleFile": "Sélectionnez un fichier à télécharger", "ui.messages.supportedFormats": "Formats pris en charge", "ui.messages.releaseToUpload": "Relâchez pour télécharger", "ui.labels.generatedAudio": "Audio généré", "ui.actions.showResult": "Aff<PERSON>r le résultat", "ui.actions.hideResult": "Masquer le résultat", "ui.messages.speechGenerating": "Génération de parole...", "Your speech is still being generated in the background. You can close this page and check the history tab for the generated audio and we will notify you when it is ready.": "Votre discours est toujours en cours de génération en arrière-plan. Vous pouvez fermer cette page et vérifier l'onglet historique pour l'audio généré, et nous vous informerons lorsqu'il sera prêt.", "downloadAudio": "Télécharger l'audio", "All Countries": "Tous les pays", "All Genders": "Tous les genres", "Country": "Pays", "Gender": "Genre", "Reset": "Réinitialiser", "Search by name or description...": "Rechercher par nom ou description...", "Male": "<PERSON><PERSON>", "Female": "<PERSON>mme", "American": "Américain", "British": "Britannique", "Australian": "Australien", "Indian": "Indien", "Chinese": "<PERSON><PERSON>", "Spanish": "Espagnol", "Canadian": "<PERSON><PERSON><PERSON>", "Irish": "Irlandais", "Singaporean": "Singapourien", "Russian": "<PERSON><PERSON>", "German": "Allemand", "Portuguese": "Portugais", "Hindi": "Hindi", "Mexican": "Mexicain", "Latin American": "<PERSON><PERSON><PERSON><PERSON>", "Argentine": "Argentine", "Peninsular": "Péninsulaire", "French": "Français", "Parisian": "<PERSON><PERSON>", "Standard": "Standard", "Brazilian": "Brésilien", "Turkish": "<PERSON><PERSON>", "Istanbul": "Istanbul", "Bavarian": "<PERSON><PERSON><PERSON>", "Polish": "Polonais", "Italian": "Italien", "South African": "Sud-africain", "Scottish": "Écossai<PERSON>", "Welsh": "<PERSON><PERSON><PERSON>", "New Zealand": "Nouvelle-Zélande", "Dutch": "Néerlandais", "Belgian": "Belge", "Swedish": "<PERSON><PERSON><PERSON><PERSON>", "Norwegian": "Norvégien", "Danish": "<PERSON><PERSON>", "Korean": "<PERSON><PERSON><PERSON>", "Korean, Seoul": "Coréen, Séoul", "Japanese": "Japonais", "Croatian": "Croate", "Czech": "Tchèque", "Moravian": "<PERSON><PERSON>", "Zealandic": "Zéland<PERSON>", "Indonesian": "Indonésien", "Javanese": "Javanais", "Romanian": "<PERSON><PERSON><PERSON><PERSON>", "Swiss": "Suisse", "Vietnamese": "<PERSON><PERSON>", "Arabic": "<PERSON><PERSON>", "Bulgarian": "Bulgare", "Finnish": "Finlandais", "Greek": "Grec", "Hungarian": "Hongrois", "Filipino": "<PERSON><PERSON>", "History": "Histoire", "imagen-flash": "Gemini 2.5 Flash", "Detail": "Détail", "Delete": "<PERSON><PERSON><PERSON><PERSON>", "ui.errors.unknownError": "Une erreur inconnue s'est produite.", "ui.errors.tryAgainLater": "Veuillez réessayer plus tard.", "More": "Plus", "tts-text": "Audio", "tts-multi-speaker": "Audio", "tts-history": "Audio", "tts-history_1": "Audio", "tts-history_2": "Audio", "tts-history_3": "Audio", "voice-training": "Entraînement vocal", "voice-training_1": "Entraînement vocal", "voice-training_2": "Entraînement de la voix", "Start writing or paste your text here or select a file to generate speech...": "Commencez à écrire ou collez votre texte ici ou sélectionnez un fichier pour générer la parole...", "Selecting a voice...": "Sélection d'une voix...", "Voices Library": "Bibliothèque des Voix", "Select a voice for your speaker from the library.": "Sélectionnez une voix pour votre haut-parleur dans la bibliothèque.", "Next": "Suivant", "Back": "Retour", "Done": "Fait", "I got it!": "Je l'ai compris !", "historyPages.endOfHistory": "Vous êtes arrivé à la fin de l'histoire.", "Press ESC to close": "Appuyez sur Échap pour fermer", "Your generated image will appear here": "Votre image générée apparaîtra ici.", "Generate Speech": "Générer un discours", "Start writing or paste your text here to generate speech...": "Commencez à écrire ou collez votre texte ici pour générer du discours...", "Video Gen": "Vidéo <PERSON>", "Generate videos from text prompts and images.": "Générez des vidéos à partir de descriptions textuelles et d'images.", "Speech Gen": "Génération de discours", "Convert text and documents to natural speech.": "Convertir du texte et des documents en discours naturel.", "Dialogue Gen": "Générateur de dialogue", "Create natural conversations with multiple speakers.": "Créer des conversations naturelles avec plusieurs interlocuteurs.", "Veo 2": "Je vois 2", "Text to Video": "Texte en vidéo", "Image to Video": "Image en vidéo", "Up to 8 seconds": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 8 secondes", "1080p Quality": "Qualité 1080p", "Multiple Styles": "Styles multiples", "Text to Speech": "Synthèse vocale", "Document to Speech": "Document à la Parole", "Multi-Speaker Support": "Prise en charge de plusieurs interlocuteurs", "50+ Voices": "Plus de 50 voix", "Multiple Languages": "Langues multiples", "Emotion Control": "Contrôle des émotions", "Multi-Speaker Dialogue": "Dialogue multi-locuteur", "Natural Conversations": "Conversations naturelles", "Voice Customization": "Personnalisation de la voix", "Emotion Expression": "Expression des émotions", "Script Generation": "Génération de script", "Audio Export": "Exportation audio", "Home": "Accueil", "Price per 1 character: {cost} Credits": "Prix par caractère : {cost} Crédits", "veo-2": "Je vois 2", "veo-3": "Je vois 3", "Your speech generation is still being generated in the background. You can close this page and check the history tab for the generated speech and we will notify you when it is ready.": "Votre génération de discours est toujours en cours de création en arrière-plan. Vous pouvez fermer cette page et consulter l'onglet historique pour le discours généré, et nous vous informerons lorsqu'il sera prêt.", "Create Another": "<PERSON><PERSON><PERSON> un autre", "estimated_credit": "<PERSON><PERSON><PERSON> estim<PERSON>", "tts-flash": "<PERSON> 2.5", "Select Another Voice": "Sélectionner une autre voix", "Custom prompt {count}": "<PERSON><PERSON><PERSON> {count}", "Custom prompt": "Invite sur mesure", "Prompt name": "Nom de l'invite", "This name will help you identify your prompt.": "Ce nom vous aidera à identifier votre invite.", "Save as new": "Enregistrer sous un nouveau", "Ok, save it!": "D'accord, enregistre-le !", "Don't use": "N'utilisez pas", "Use": "Utiliser", "You have the right to use the speech output generated by our services for personal, educational, or commercial purposes.": "Vous avez le droit d'utiliser la sortie vocale générée par nos services à des fins personnelles, éducatives ou commerciales.", "Your saved prompts": "<PERSON><PERSON> invites enregistrées", "Success": "Su<PERSON>ès", "Saved prompt successfully": "Invite enregis<PERSON><PERSON> avec succès", "Error": "<PERSON><PERSON><PERSON>", "Saved prompt failed": "La sauvegarde de l'invite a échoué.", "Updated prompt successfully": "Mise à jour de l'invite ré<PERSON>ie.", "Updated prompt failed": "Échec de la mise à jour de l'invite.", "Are you sure you want to delete this prompt?": "Êtes-vous sûr de vouloir supprimer cette invite ?", "Deleted prompt successfully": "De<PERSON>e supprimée avec succès", "Deleted prompt failed": "Suppression de l'invite a échoué", "Enter your custom prompt here.": "Entrez votre invite personnalisée ici.", "Ex: Funny prompt": "Ex : <PERSON><PERSON><PERSON> d'invite", "Discard": "<PERSON><PERSON>", "Update": "Mettre à jour", "Edit": "Modifier", "Custom Prompt": "<PERSON><PERSON><PERSON>", "Your credits will never expire.": "Vos crédits n'expireront jamais.", "Available credits": "Crédits disponibles", "{n}+ Styles": "{n}+ Styles", "Create images from text prompts.": "Créer des images à partir de suggestions textuelles.", "/Image": "/Image", "/Video": "Vidéo", "/1 character": "/1 caractère", "Buy credits": "Acheter des crédits", "My Account": "Mon compte", "Manage your account, credits, and orders.": "<PERSON><PERSON><PERSON> votre compte, vos crédits et vos commandes.", "Full Name": "Nom complet", "Total Available Credits": "Cré<PERSON>s <PERSON>ux Disponibles", "Locked Credits": "Cré<PERSON><PERSON> bloq<PERSON>", "Save changes": "Enregistrer les modifications", "Your account has been updated.": "Votre compte a été mis à jour.", "This field is required.": "Ce champ est requis.", "User Info": "Informations utilisateur", "Email": "<PERSON><PERSON><PERSON>", "Used to sign in, for email receipts and product updates.": "Utilisé pour se connecter, pour les reçus par e-mail et les mises à jour de produits.", "Active and valid credits only": "Crédits actifs et valides uniquement.", "We lock your credits to perform transactions.": "Nous verrouillons vos crédits pour effectuer des transactions.", "Referral Link": "<PERSON><PERSON> de <PERSON>", "Share your referral link to earn credits.": "Partagez votre lien de parrainage pour gagner des crédits.", "Referral Code": "Code de parrainage", "Your Referral Code": "Votre code de parrainage", "Copy": "<PERSON><PERSON><PERSON>", "Copied!": "Copié !", "Orders": "Commandes", "Manage your orders.": "<PERSON><PERSON><PERSON> vos commandes.", "Will appear on receipts, invoices, and other communication.": "Apparaîtra sur les reçus, factures et autres communications.", "User Information": "Informations utilisateur", "Must be at least 8 characters": "Doit comporter au moins 8 caractères", "Passwords must be different": "Les mots de passe doivent être différents.", "Passwords must match": "Les mots de passe doivent correspondre.", "Current password": "Mot de passe actuel", "New password": "Nouveau mot de passe", "Confirm new password": "Confirmer le nouveau mot de passe", "Password": "Mot de passe", "Confirm your current password before setting a new one.": "Confirmez votre mot de passe actuel avant d'en définir un nouveau.", "Account": "<PERSON><PERSON><PERSON>", "No longer want to use our service? You can delete your account here. This action is not reversible. All information related to this account will be deleted permanently.": "Vous ne souhaitez plus utiliser notre service ? Vous pouvez supprimer votre compte ici. Cette action est irréversible. Toutes les informations liées à ce compte seront supprimées définitivement.", "Delete account": "Supprimer le compte", "Change Password": "Modifier le mot de passe", "Security": "Sécurité", "Credit Statistics": "Statistiques de crédit", "enhance_prompt": "Améliorer l'invite", "Current Plan": "Plan actuel", "When you buy credits, you will be upgraded to Premium Plan.": "Lorsque vous achetez des crédits, vous serez mis à niveau vers le plan Premium.", "Available Credits": "Crédits disponibles", "Purchased Credits": "<PERSON><PERSON><PERSON><PERSON>", "Plan Credits": "Crédits du Plan", "profile.passwordChanged": "Mot de passe modifié", "profile.passwordChangedDescription": "Votre mot de passe a été modifié avec succès.", "profile.passwordChangeError": "Échec du changement de mot de passe", "profile.passwordChangeErrorDescription": "Une erreur s'est produite lors du changement de votre mot de passe. Veuillez réessayer.", "delete": "<PERSON><PERSON><PERSON><PERSON>", "profile.deleteAccount": "Supprimer le compte", "profile.deleteAccountConfirmation": "Êtes-vous sûr de vouloir supprimer votre compte ? Cette action est irréversible et toutes vos données seront définitivement perdues.", "profile.accountDeleted": "Compte supprimé", "profile.accountDeletedDescription": "Votre compte a été supprimé avec succès.", "profile.accountDeletionError": "Échec de la suppression du compte", "profile.accountDeletionErrorDescription": "Une erreur s'est produite lors de la suppression de votre compte. Veuillez réessayer.", "To celebrate our launch, enjoy 97% off select Gemini API models. Offer valid until further notice.": "Pour célébrer notre lancement, profitez de 50 % de réduction sur certains modèles API Gemini. Offre valable jusqu'à nouvel ordre.", "Check now": "Vérifiez maintenant", "payment.success.title": "Paiement ré<PERSON> !", "payment.success.message": "Merci pour votre achat ! Votre paiement a été traité avec succès.", "payment.success.orderId": "Identifiant de commande :", "payment.success.redirecting": "Redirection vers vos commandes dans {seconds} secondes...", "payment.success.viewOrders": "Voir mes commandes", "payment.error.title": "<PERSON><PERSON><PERSON> de paiement", "payment.error.message": "Un problème est survenu lors du traitement de votre paiement. Veuillez contacter le support si cela persiste.", "payment.error.backToOrders": "Retour aux commandes", "Overview of your credits status.": "Aperçu de l'état de vos crédits.", "Payment History": "Historique des paiements", "Your payment history will appear here once you have made a purchase.": "Votre historique de paiement apparaîtra ici une fois que vous aurez effectué un achat.", "Payment method": "Moyen de paiement", "Purchase Date": "Date d'achat", "Amount": "<PERSON><PERSON>", "Status": "Statut", "Payment amount": "Montant du paiement", "payment.status.unavailable": "Indisponible", "payment.status.created": "<PERSON><PERSON><PERSON>", "payment.status.completed": "<PERSON><PERSON><PERSON><PERSON>", "payment.status.failed": "<PERSON><PERSON><PERSON>", "payment.status.canceled": "<PERSON><PERSON><PERSON>", "payment.status.processing": "Traitement", "payment.status.refund": "Remboursement", "payment.status.partial_paid": "Partiellement payé", "apiKeys.successTitle": "Clé API créée avec succès", "apiKeys.importantNotice": "<PERSON>vis important", "apiKeys.copyWarning": "C'est la seule fois où vous pourrez voir et copier cette clé API. Veuillez la copier maintenant et la conserver en lieu sûr.", "apiKeys.key": "Clé API", "apiKeys.securityTip": "Conseils de sécurité :", "apiKeys.tip1": "<PERSON>z cette clé dans un endroit sûr.", "apiKeys.tip2": "Ne partagez jamais votre clé API publiquement.", "apiKeys.tip3": "<PERSON> compromis, supprimez cette clé et créez-en une nouvelle.", "apiKeys.copyFirst": "Co<PERSON>z d'abord la clé API", "common.done": "Fait", "Integration": "Intégration", "API Keys": "Clés API", "Manage your API keys.": "Gérez vos clés API.", "Create API Key": "<PERSON><PERSON><PERSON> une clé API", "Name your API key.": "Nommez votre clé API.", "Create": "<PERSON><PERSON><PERSON>", "You have not created any API keys yet.": "Vous n'avez pas encore créé de clés API.", "Copy API Key": "Copier la clé API", "Delete API Key": "Supprimer la clé API", "EMAIL_NOT_EXIST": "L'e-mail n'existe pas.", "Your account is not verified": "Votre compte n'est pas vérifié.", "Your account is not verified. Please verify your account to continue": "Votre compte n'est pas vérifié. Veuillez vérifier votre compte pour continuer.", "TOKEN_USED": "Jeton déjà utilisé", "NOT_ENOUGH_CREDIT": "Crédit insuffisant. Veuillez recharger votre compte.", "Not enough credit": "Pas assez de c<PERSON>", "Your account does not have enough credit. Please top up your account to continue.": "Votre compte n'a pas suffisamment de crédit. Veuillez recharger votre compte pour continuer.", "{n} credits": "{n} crédits", "USD / {unit}": "USD / {unit}", "Credits / {unit}": "Crédits / {unit}", "Save {n}%": "Économisez {n}%", "${price} = {n} credits": "${price} = {n} crédits", "You can switch between money and credits to see the price in your preferred currency.": "Vous pouvez alterner entre argent et crédits pour voir le prix dans votre devise préférée.", "Forever": "Pour toujours", "For large organizations.": "Pour les grandes organisations.", "Free": "<PERSON><PERSON><PERSON>", "Contact us": "Contactez-nous", "Contact sales": "<PERSON><PERSON> les ventes", "Premium": "Premium", "Enterprise": "Entreprise", "Show money": "<PERSON><PERSON> l'argent", "Show credits": "Afficher les crédits", "Auto upgrade after buy credits": "Mise à niveau automatique après l'achat de crédits", "Image": "Image", "Video": "Vidéo", "Audio": "Audio", "Dialog": "Dialogue", "Get started": "Co<PERSON><PERSON>z", "Contact": "Contact", "Image Style": "Style de l'image", "Image Aspect Ratio": "Ratio d'aspect de l'image", "Enhance Prompt": "Améliorer l'invite", "Aspect Ratio": "Format d'image", "Support multiple aspect ratio": "Prend en charge plusieurs formats d'image", "Support enhance prompt": "Soutenir améliorer promptement", "Up to {size}MB": "Jusqu'à {size} Mo", "Budget Calculator": "Calculateur de budget", "Resource Calculator": "Calculateur de ressources", "Budget Amount": "Montant du budget", "Resources you can generate:": "Ressources que vous pouvez gén<PERSON>rer :", "Select resources you want:": "Sélectionnez les ressources que vous souhaitez :", "credits": "crédits", "image": "image", "video": "vidéo", "per item": "par article", "Quantity": "Quantité", "Total Cost:": "Coût total :", "Approximately {credits} credits": "Environ {credits} crédits", "Images": "Images", "Videos": "Vid<PERSON><PERSON>", "Pricing Calculator": "Calculateur de prix", "Calculate how many resources can you generate with your budget.": "Calculez combien de ressources vous pouvez générer avec votre budget.", "Minimum $10 required": "Montant minimum requis : 10 $", "Minimum Purchase Required": "Achat Minimum Requis", "Minimum purchase amount is $10. Please increase your selection.": "Le montant minimum d'achat est de 10 $. Veuillez augmenter votre sélection.", "Minimum purchase amount is $10": "Montant d'achat minimum est de 10 $.", "Please add more resources to reach the minimum purchase amount.": "Veuillez ajouter plus de ressources pour atteindre le montant minimum d'achat.", "Enter exact number": "Entrez le nombre exact.", "Enter budget amount": "Entrez le montant du budget", "Min: $10": "Min : 10 $", "Each amount shows what you can generate with your entire budget (choose one type)": "Chaque montant montre ce que vous pouvez générer avec l'ensemble de votre budget (choisissez un type).", "OR": "OU", "AI Image Generation Examples": "Exemples de génération d'images par IA", "Explore the power of AI image generation with these interactive comparisons": "Explorez le pouvoir de la génération d'images par IA avec ces comparaisons interactives.", "Try the Comparison!": "Essayez la comparaison !", "Drag the slider left and right to compare the before and after images. You can also click anywhere on the image to move the slider.": "Faites glisser le curseur vers la gauche et la droite pour comparer les images avant et après. Vous pouvez également cliquer n'importe où sur l'image pour déplacer le curseur.", "Got it!": "Compris !", "Please login to access your saved prompts.": "Veuillez vous connecter pour accéder à vos invites enregistrées.", "Access Your Personal Voices": "Accédez à vos voix personnelles", "Access Your Favorite Voices": "Accédez à vos voix préférées", "Login to view and manage your personal voice collection. Upload custom voices and access them anytime.": "Connectez-vous pour voir et gérer votre collection vocale personnelle. Téléchargez des voix personnalisées et accédez-y à tout moment.", "Login to view your favorite voices. Save voices you love and access them quickly for your projects.": "Connectez-vous pour voir vos voix préférées. Enregistrez les voix que vous aimez et accédez-y rapidement pour vos projets.", "Create Account": "<PERSON><PERSON><PERSON> un compte", "Join thousands of creators using AI voices for their projects": "Rejoignez des milliers de créateurs utilisant des voix IA pour leurs projets.", "AI Video Generation Examples": "Exemples de génération de vidéos par IA", "Explore the power of AI video generation with these interactive comparisons": "Explorez la puissance de la génération vidéo par IA avec ces comparaisons interactives.", "Try the Video Comparison!": "Essayez la comparaison vidéo !", "Drag the slider left and right to compare text prompts/images with generated videos. You can also click anywhere to move the slider.": "Faites glisser le curseur à gauche et à droite pour comparer les invites textuelles/images avec les vidéos générées. Vous pouvez également cliquer n'importe où pour déplacer le curseur.", "Duration": "<PERSON><PERSON><PERSON>", "Select video duration in seconds": "Sélectionnez la durée de la vidéo en secondes", "This setting is locked for the selected model": "Ce paramètre est verrouillé pour le modèle sélectionné.", "Prompts will always be refined to improve output quality (required for this model)": "Les invites seront toujours affinées pour améliorer la qualité de sortie (requis pour ce modèle).", "SIGNUP_MAIL_EXIST": "L'email existe déjà.", "SIGNIN_USER_NOT_FOUND": "Utilisateur non trouvé", "SIGNIN_USER_NOT_VERIFIED": "Utilisateur non vérifié", "SIGNIN_USER_DISABLED": "Utilisateur désactivé", "SIGNIN_WRONG_PASSWORD": "Mot de passe incorrect", "SIGNIN_USER_NOT_FOUND_FOR_EMAIL": "Utilisateur non trouvé pour cet e-mail", "SIGNIN_INVALID_EMAIL": "E-mail invalide", "auth.accountCreated": "<PERSON><PERSON><PERSON>", "auth.accountCreatedDescription": "Votre compte a été créé avec succès. Veuillez vérifier votre e-mail pour valider votre compte.", "Select voice on right": "Sélectionner la voix à droite", "privacy.lastUpdated": "Dernière mise à jour :", "privacy.lastUpdatedDate": "15 janvier 2025", "privacy.introduction": "Chez GeminiGen.AI, nous accordons la priorité à la protection de votre vie privée et à la sécurité de vos informations personnelles. Cette politique de confidentialité décrit comment nous collectons, utilisons et protégeons les informations que vous fournissez lors de l'utilisation de nos services de génération de contenu IA, y compris la génération d'images, de vidéos, de synthèse vocale et de génération de dialogues. En accédant et en utilisant notre site web (geminigen.ai), vous consentez aux pratiques décrites dans cette politique.", "privacy.informationCollectionDescription": "Lorsque vous créez un compte sur notre site web, nous collectons certaines informations personnelles telles que votre adresse e-mail et votre nom complet. Ces informations sont nécessaires pour vous donner accès à nos services, fournir des mises à jour ou des modifications de nos services, et pour des analyses statistiques visant à améliorer nos offres. De plus, tout texte, image ou document téléchargé pour la génération de contenu par l'IA est stocké temporairement uniquement dans le but de générer le rendu.", "privacy.creditCalculation": "Calcul du Crédit", "privacy.creditCalculationDescription": "Pour garantir une facturation précise, le nombre de crédits nécessaires pour la génération de contenu IA est calculé en fonction du texte, des images ou des documents fournis. Ce calcul est effectué à l'aide de notre algorithme propriétaire et est directement proportionnel à la complexité et à la longueur de l'entrée.", "privacy.paymentSecurity": "3. Paiement et Sécurité", "privacy.paymentSecurityDescription": "Pour le traitement des paiements, nous proposons des options PayPal et carte de crédit. Nous ne stockons pas les informations de carte de crédit sur nos serveurs. Toutes les transactions de paiement sont gérées de manière sécurisée par des prestataires de services de paiement tiers de confiance, conformément à leurs politiques de confidentialité et de sécurité respectives.", "privacy.emailNotification": "Notification par e-mail et accès au contenu généré", "privacy.emailNotificationDescription": "Une fois la génération de contenu terminée, vous recevrez une notification par email contenant un lien sécurisé pour accéder et télécharger le contenu généré (images, vidéos, fichiers audio ou dialogues). Ce lien reste actif pendant une période spécifiée pour votre commodité.", "privacy.thirdPartyServices": "6. Services de tiers", "privacy.thirdPartyServicesDescription": "Nous pouvons utiliser des services tiers, tels que des fournisseurs d'analyses, pour améliorer nos services et analyser les habitudes d'utilisation. Ces services peuvent recueillir des informations sur votre utilisation mais n'ont pas accès à vos informations personnelles.", "privacy.cookies": "7. Cookies et technologies de suivi", "privacy.cookiesDescription": "Notre site web utilise des cookies et des technologies de suivi similaires pour améliorer l'expérience utilisateur et analyser l'utilisation du site web. Vous avez la possibilité de désactiver les cookies via les paramètres de votre navigateur, mais veuillez noter que certaines fonctionnalités de notre site web pourraient ne pas fonctionner correctement en conséquence.", "privacy.thirdPartyLinks": "8. Liens de tiers", "privacy.thirdPartyLinksDescription": "Notre site web peut contenir des liens vers des sites web tiers. Nous ne sommes pas responsables des pratiques de confidentialité ou du contenu de ces sites web et vous encourageons à consulter leurs politiques de confidentialité respectives.", "privacy.childrenPrivacy": "9. Confidentialité des enfants", "privacy.childrenPrivacyDescription": "Nos services ne sont pas destinés aux personnes de moins de 18 ans, et nous ne collectons ni ne stockons sciemment des informations personnelles de quiconque ayant moins de cet âge. Si nous prenons connaissance d'une collecte involontaire d'informations personnelles d'un enfant de moins de 18 ans, nous prendrons des mesures pour supprimer ces informations de nos dossiers.", "privacy.policyChanges": "10. <PERSON><PERSON> à jour de notre politique de confidentialité", "privacy.policyChangesDescription": "Nous pouvons périodiquement mettre à jour notre politique de confidentialité afin de refléter les changements dans nos pratiques ou les exigences légales. Toute révision sera effective immédiatement après la publication de la politique mise à jour sur notre site internet. Nous vous encourageons à consulter régulièrement cette politique de confidentialité pour obtenir les informations les plus récentes.", "privacy.commercialUse": "11. Utilisation commerciale", "privacy.commercialUseDescription": "Vous avez le droit d'utiliser le contenu généré par nos services à des fins personnelles, éducatives ou commerciales. Cependant, vous ne pouvez pas revendre, redistribuer ou concéder sous licence le contenu généré sans le consentement écrit préalable de GeminiGen.AI.", "privacy.otherPeoplePrivacy": "12. La vie privée des autres", "privacy.otherPeoplePrivacyDescription": "Vous devez respecter la vie privée des autres lorsque vous utilisez nos services. Ne téléchargez pas ou ne créez pas de contenu contenant des informations personnelles, des données confidentielles ou du matériel protégé par des droits d'auteur sans permission.", "privacy.unsubscribe": "13. <PERSON><PERSON>", "privacy.unsubscribeDescription": "Vous pouvez vous désabonner de la publicité ciblée en cliquant sur le bouton 'Basculer' dans les paramètres de votre profil.", "terms.lastUpdated": "Dernière mise à jour :", "terms.lastUpdatedDate": "15 janvier 2025", "terms.introduction": "Bienvenue chez GeminiGen.AI. Ces Conditions d'Utilisation régissent votre utilisation de nos services de génération de contenu alimentés par l'IA, y compris la génération d'images, la génération de vidéos, la synthèse vocale et la génération de dialogues.", "terms.acceptanceOfTermsDetails": "Votre utilisation continue de nos services constitue une acceptation de toute modification de ces Conditions.", "terms.serviceDescription": "2. Description des services", "terms.serviceDescriptionText": "GeminiGen.AI fournit des services de génération de contenu alimentés par l'IA, y compris mais sans s'y limiter :", "terms.serviceUsageDescription": "Vous vous engagez à utiliser nos services uniquement à des fins légales. Vous vous abstiendrez de télécharger, transmettre ou stocker tout contenu illégal, nuisible, diffamatoire ou portant atteinte aux droits d'autrui. Vous êtes seul responsable de tout contenu soumis pour la génération de contenu par IA.", "terms.permittedUse": "4.1 Utilisation Autorisée", "terms.permitted1": "Utilisez nos services à des fins légales, créatives et commerciales.", "terms.permitted2": "Créer du contenu conforme aux lois et réglementations en vigueur.", "terms.permitted3": "Respectez les droits de propriété intellectuelle d'autrui.", "terms.permitted4": "Utilisez le contenu généré conformément à nos conditions de licence.", "terms.prohibitedUse": "Utilisation interdite 4.2", "terms.prohibited1": "<PERSON><PERSON><PERSON>rez du contenu illégal, nuisible, mena<PERSON><PERSON>, abusif ou discriminatoire.", "terms.prohibited2": "<PERSON><PERSON><PERSON> du contenu qui viole les droits de propriété intellectuelle d'autrui", "terms.prohibited3": "Produire du contenu destiné à tromper, frauder ou induire en erreur autrui", "terms.prohibited4": "Générer du contenu représentant des mineurs dans des situations inappropriées", "terms.prohibited5": "<PERSON><PERSON><PERSON> du contenu qui promeut la violence, le terrorisme ou des activités illégales", "terms.prohibited6": "Utiliser nos services pour spammer, harceler ou nuire à autrui.", "terms.prohibited7": "Tenter de r<PERSON>ir, de pirater ou de compromettre nos systèmes", "terms.prohibited8": "Enfreindre toute loi ou réglementation applicable", "terms.userAccounts1": "Fournir des informations d'enregistrement précises et complètes", "terms.userAccounts2": "Maintenir la sécurité et la confidentialité de vos identifiants de compte", "terms.userAccounts3": "Toutes les activités qui se déroulent sous votre compte", "terms.userAccounts4": "Nous informer immédiatement de toute utilisation non autorisée de votre compte.", "terms.userAccounts5": "Assurer que les informations de votre compte restent à jour et exactes.", "terms.paymentAndBilling": "3. Paiement et Crédits", "terms.paymentAndBillingDescription": "Nos services de génération de contenu par IA fonctionnent sur un système basé sur des crédits. Le nombre de crédits nécessaires pour la création de contenu est déterminé par notre algorithme propriétaire et est calculé avec précision en fonction de la complexité de l'entrée et des exigences de sortie.", "terms.payment1": "Une fois votre solde de crédit <PERSON>, vous devez recharger votre compte.", "terms.payment2": "Les paiements peuvent être effectués via PayPal ou carte de crédit.", "terms.payment3": "Tous les paiements sont traités en toute sécurité par des processeurs de paiement tiers.", "terms.payment4": "Les crédits ne sont pas remboursables, sauf si la loi applicable l'exige.", "terms.payment5": "Les prix sont susceptibles de changer avec un préavis raisonnable.", "terms.payment6": "Vous êtes responsable de toutes les taxes et frais applicables.", "terms.ourIntellectualProperty": "5.1 Notre propriété intellectuelle", "terms.ourIntellectualPropertyDescription": "GeminiGen.AI et ses services, y compris tous les logiciels, algorithmes, designs et contenus, sont protégés par les lois sur la propriété intellectuelle. Vous ne pouvez pas copier, modifier, distribuer ou créer des œuvres dérivées sans notre autorisation écrite expresse.", "terms.userGeneratedContent": "5.2 Contenu généré par l'utilisateur", "terms.userGeneratedContentDescription": "Vous conservez la propriété du contenu que vous créez en utilisant nos services, sous réserve des points suivants :", "terms.userContent1": "Vous nous accordez une licence limitée pour traiter et stocker votre contenu afin de fournir nos services.", "terms.userContent2": "Vous déclarez avoir le droit d'utiliser tout contenu que vous fournissez.", "terms.userContent3": "Vous êtes responsable de vous assurer que votre contenu généré est conforme à ces conditions.", "terms.userContent4": "Nous pouvons retirer le contenu qui enfreint nos politiques ou les lois en vigueur.", "terms.privacyAndDataProtection": "7. Protection de la confidentialité et des données", "terms.privacyAndDataProtectionDescription": "Votre vie privée est importante pour nous. Notre collecte, utilisation et protection de vos informations personnelles sont régies par notre politique de confidentialité, qui est intégrée dans ces conditions par référence.", "terms.serviceAvailability": "8. Disponibilité du service", "terms.serviceAvailabilityDescription": "Bien que nous nous efforcions de fournir des services fiables, nous ne garantissons pas un accès ininterrompu. Nos services peuvent être temporairement indisponibles en raison de maintenance, de mises à jour ou de problèmes techniques. Nous nous réservons le droit de modifier ou d'interrompre les services avec un préavis raisonnable.", "terms.terminationByUser": "8.1 Résiliation par Vous", "terms.terminationByUserDescription": "Vous pouvez résilier votre compte à tout moment en contactant notre équipe d'assistance. À la résiliation, votre accès à nos services cessera, mais les présentes Conditions continueront de s'appliquer à votre utilisation antérieure de nos services.", "terms.terminationByUs": "8.2 Résiliation par Nous", "terms.terminationByUsDescription": "Nous pouvons suspendre ou résilier votre compte et l'accès à nos services immédiatement, avec ou sans préavis, pour l'une des raisons suivantes :", "terms.termination1": "Violation de ces conditions d'utilisation", "terms.termination2": "Activité frauduleuse, abusive ou illégale", "terms.termination3": "Non-paiement des frais ou charges", "terms.termination4": "Périodes prolongées d'inactivité", "terms.termination5": "Exigences légales ou réglementaires", "terms.termination6": "Protection de nos droits, de notre propriété ou de notre sécurité.", "terms.limitationOfLiability": "6. Limitation de responsabilité", "terms.limitationOfLiabilityDescription": "GeminiGen.AI ne pourra être tenu responsable de tout dommage direct, indirect, accessoire, spécial ou consécutif découlant de ou lié à votre utilisation de nos services. Nous ne garantissons pas l'exactitude, l'exhaustivité ou la disponibilité des services et déclinons toute garantie, expresse ou implicite, concernant leur utilisation ou leurs résultats.", "terms.disclaimer1": "Garanties de qualité marchande, d'adaptation à un usage particulier et de non-contrefaçon", "terms.disclaimer2": "Garanties concernant l'exactitude, la fiabilité ou la qualité du contenu généré", "terms.disclaimer3": "Responsabilité quant à l'utilisation ou la distribution du contenu généré", "terms.disclaimer4": "Responsabilité pour tout dommage résultant d'interruptions de service ou de problèmes techniques.", "terms.indemnification": "12. Indemnisation", "terms.indemnificationDescription": "Vous acceptez d'indemniser, de défendre et de dégager de toute responsabilité GeminiGen.AI et ses affiliés de toute réclamation, dommage, perte ou dépense résultant de votre utilisation de nos services, de la violation de ces Conditions ou de l'atteinte aux droits de tiers.", "terms.governingLaw": "9. <PERSON><PERSON> <PERSON>", "terms.governingLawDescription": "Ces Conditions d'utilisation doivent être interprétées et régies conformément aux lois du Viêt Nam, sans égard à ses principes de conflit de lois. Tout litige résultant de ou lié à ces Conditions et à l'utilisation de nos services sera soumis à la juridiction exclusive des tribunaux du Viêt Nam.", "terms.clarificationOpenAI": "10. Clarification concernant les services d'IA tiers", "terms.clarificationOpenAIDescription": "GeminiGen.AI est une entité indépendante et n'est pas affiliée à OpenAI, Google ou d'autres fournisseurs de services d'IA. Nos services de génération de contenu utilisent diverses API d'IA pour convertir du texte en images, vidéos, discours et dialogues, mais nous opérons indépendamment de ces fournisseurs. Cette clarification est faite pour éviter toute confusion ou malentendu concernant la relation entre GeminiGen.AI et les fournisseurs de services d'IA tiers. Les utilisateurs doivent être conscients que bien que nous utilisions la technologie d'IA de tiers pour fournir nos services, GeminiGen.AI est seul responsable du fonctionnement de nos services et du respect de ces Conditions d'utilisation.", "terms.contactEmail": "E-mail :", "terms.contactAddress": "<PERSON><PERSON><PERSON> :", "terms.companyAddress": "Le site web est exploité conjointement par A2ZAI LTD No:16078579 Adresse enregistrée au 483 Green Lanes, London, England, N13 4BS", "footer.supportEmail": "Support par email", "footer.address": "<PERSON><PERSON><PERSON>", "footer.companyAddress": "Le site web est exploité conjointement par A2ZAI LTD No:16078579 Adresse enregistrée au 483 Green Lanes, London, England, N13 4BS", "second": "seconde", "1M characters": "1 million de caractères", "Support {n}+ voices": "<PERSON><PERSON><PERSON> en charge {n}+ voix", "Support {n}+ emotions": "Soutenir {n}+ émotions", "Support {n}+ languages": "Prise en charge de {n}+ langues", "Support custom prompt": "Soutenir l'invite personnalisée", "Support MP3 and WAV": "Prend en charge MP3 et WAV", "Support speed control": "Contrôle de la vitesse de soutien", "Support document to speech": "Document de support à la parole", "Current plan": "Plan actuel", "Already premium": "Déjà premium", "Gemini 2.5 Flash": "Gemini 2.5 Flash", "Gemini 2.5 Pro": "Gémeaux 2.5 Pro", "Voice": "Voix", "Emotion": "Émotion", "Language": "<PERSON><PERSON>", "Output Format": "Format de sortie", "Speed": "Vitesse", "Document": "Document", "Enjoy 97% off select Gemini API models. Offer valid until further notice.": "Profitez de 50 % de réduction sur une sélection de modèles API Gemini. Offre valable jusqu'à nouvel ordre.", "Text must be at least 4 characters long.": "Le texte doit contenir au moins 4 caractères.", "Each dialog text must be at least 4 characters long.": "Chaque texte de dialogue doit comporter au moins 4 caractères.", "Please enter text or select a file to generate speech.": "Veuillez entrer du texte ou sélectionner un fichier pour générer de la parole.", "Please select a voice for speech generation.": "Veuillez sélectionner une voix pour la génération vocale.", "Please add at least one dialog to generate speech.": "Veuillez ajouter au moins un dialogue pour générer une parole.", "Please select voices for both speakers.": "Veuillez choisir des voix pour les deux interlocuteurs.", "Photorealistic": "Photoréaliste", "imageStyles.photorealistic.description": "Image réaliste avec un haut niveau de détail et de résolution.", "Delete Voice": "Supprimer la voix", "Are you sure you want to delete this voice? This action cannot be undone.": "Êtes-vous sûr de vouloir supprimer cette voix ? Cette action ne peut pas être annulée.", "Voice deleted successfully": "Voix supprimée avec succès", "Failed to delete voice": "Échec de la suppression de la voix.", "Create your first voice": "<PERSON><PERSON>ez votre première voix", "Create Custom Voice": "<PERSON><PERSON>er une voix personnalisée", "Type of voice to create": "Type de voix à créer", "Instant Voice Cloning": "Clonage vocal instantané", "Clone a voice from a clean sample recording. Samples should contain 1 speaker and be over 1 minute long and not contain background noise": "<PERSON><PERSON>r une voix à partir d'un échantillon d'enregistrement propre. Les échantillons doivent contenir 1 locuteur, durer plus d'une minute et ne pas contenir de bruit de fond.", "Professional Voice Cloning": "Clonage vocal professionnel", "Create the most realistic digital replica of your voice.": "C<PERSON>ez la réplique numérique la plus réaliste de votre voix.", "Speaker Name": "Nom de l'orateur", "Enter speaker name": "Saisissez le nom de l'orateur", "Describe the voice characteristics": "Décrivez les caractéristiques de la voix.", "Select gender": "Sélectionner le genre", "Select age": "Sélectionner l'âge", "Select accent": "Sélectionner l'accent", "Audio Sample": "Échantillon audio", "I agree to the privacy policy": "J'accepte la politique de confidentialité.", "Note:": "Remarque :", "The sound should be clear, without any noise, and last around 1 minutes to ensure good quality.": "Le son doit être clair, sans aucun bruit, et durer environ 1 minute pour garantir une bonne qualité.", "It will cost 0 credits each time you create a voice.": "Cela coûtera 0 crédits chaque fois que vous créez une voix.", "Young": "<PERSON><PERSON>", "Middle": "Milieu", "Old": "Vieux", "English": "<PERSON><PERSON><PERSON>", "Custom voice created successfully": "Voix personnalisée créée avec succès.", "Failed to create custom voice": "Échec de la création de la voix personnalisée.", "validation.mustAgreeToPrivacy": "<PERSON><PERSON> <PERSON> accepter la politique de confidentialité.", "I agree to the {0}": "J'accepte le {0}", "Privacy Policy": "Politique de confidentialité", "The sound should be clear, without any noise, and last around 10 minutes to ensure good quality.": "Le son doit être clair, sans aucun bruit, et durer environ 10 minutes pour garantir une bonne qualité.", "It will cost 5,000 credits each time you create a voice.": "<PERSON><PERSON> 5 000 crédits chaque fois que vous créez une voix.", "Maximum file size": "Taille maximale du fichier", "File size exceeds maximum limit of {maxSize}": "La taille du fichier dépasse la limite maximale de {maxSize}", "File size exceeds 150MB limit": "La taille du fichier dépasse la limite de 150 Mo.", "ui.errors.viewGooglePolicy": "Voir la politique d'utilisation de l'IA générative de Google", "negativePrompt": "Negative Prompt", "negativePromptDescription": "Describe what you don't want to see in the video", "negativePromptTooltip": "Negative prompts help exclude unwanted elements, styles, or concepts from your video generation. For example: 'blurry, low quality, distorted faces'", "negativePromptPlaceholder": "Enter what you want to avoid in the video...", "negativePromptSuggestions": "Quick suggestions", "negativePromptSuggestion1": "blurry, low quality", "negativePromptSuggestion2": "distorted faces, deformed", "negativePromptSuggestion3": "text, watermark", "negativePromptSuggestion4": "dark, underexposed", "negativePromptSuggestion5": "shaky, unstable motion", "negativePromptSuggestion6": "pixelated, artifacts", "negative_prompt": "Invite à ne pas faire", "aspect_ratio": "Format d'image", "person_generation": "Génération de Personnes", "ALLOW_ADULT": "Autoriser adulte", "ALLOW_ALL": "Autoriser tout", "veo-3-fast": "Je vois 3 rapides", "Click to copy. UUID is unique and can be used to contact support.": "Cliquez pour copier. L'UUID est unique et peut être utilisé pour contacter le support.", "Account Activation Required": "Activation du Compte Requise", "Please activate your account to access your personal voice collection. Check your email for the activation link.": "Veuillez activer votre compte pour accéder à votre collection vocale personnelle. Vérifiez votre e-mail pour le lien d'activation.", "Please activate your account to access your favorite voices. Check your email for the activation link.": "Veuillez activer votre compte pour accéder à vos voix préférées. Vérifiez votre email pour le lien d'activation.", "Resend Activation Email": "Renvoyer l'e-mail d'activation", "Check Email": "Vérifier les courriels", "Didn't receive the email? Check your spam folder or try resending.": "Vous n'avez pas reçu l'e-mail ? Vérifiez votre dossier spam ou essayez de le renvoyer.", "Activation email sent": "E-mail d'activation envoyé", "Please check your email for the activation link.": "Veuillez vérifier votre courriel pour le lien d'activation.", "Voice Training Started": "Démarrage de la formation vocale", "Your custom voice is being trained. You will be notified when it's ready.": "Votre voix personnalisée est en cours d'entraînement. Vous serez averti lorsqu'elle sera prête.", "Voice Training Complete": "Entraînement vocal terminé", "Your custom voice is ready to use!": "Votre voix personnalisée est prête à être utilisée !", "Voice Training Failed": "Échec de la formation vocale.", "Voice training failed. Please try again.": "L'entraînement vocal a échoué. Veuillez réessayer.", "Training": "Entraînement", "Failed": "<PERSON><PERSON><PERSON>", "Voice Training in Progress": "Entraînement vocal en cours.", "Your custom voice is being trained. This process may take up to 30 minutes. You will be notified when it's ready.": "Votre voix personnalisée est en cours d'entraînement. Ce processus peut prendre jusqu'à 30 minutes. Vous serez informé lorsqu'elle sera prête.", "Voice Name": "Nom de voix", "Training Type": "Type de formation", "Training Progress": "Progrès de la formation", "Estimated time: 5-30 minutes": "Temps estimé : 5 à 30 minutes", "Refresh Status": "Actualiser le statut", "Voice Not Available": "Voix non disponible", "This voice is currently being trained and cannot be selected yet.": "Cette voix est actuellement en cours d'entraînement et ne peut pas encore être sélectionnée.", "hero.title": "C<PERSON>er un contenu IA incroyable", "hero.subtitle": "Transformez vos idées en superbes images, vidéos, discours générés par l'IA et bien plus encore. Découvrez l'avenir de la génération de contenu créatif.", "hero.cta.primary": "Commencez à créer", "hero.cta.secondary": "Regarder la démo", "hero.users": "Approuvé par plus de 10 000 créateurs dans le monde entier", "hero.scroll": "Faites défiler pour explorer", "features.title": "Principales caractéristiques", "features.subtitle": "Découvrez des fonctionnalités puissantes qui vous aident à créer du contenu IA professionnel.", "features.ai.title": "Intelligence artificielle avancée", "features.ai.description": "Utilisez une technologie d'IA de pointe pour générer du contenu de haute qualité avec une précision incroyable.", "features.speed.title": "Génération Rapide", "features.speed.description": "Transformez vos idées en contenu en quelques secondes seulement. Pas de longs temps d'attente.", "features.creative.title": "Créativité Illimitée", "features.creative.description": "Créez du contenu dans n'importe quel style, de l'animation au réaliste, de l'artistique au professionnel.", "features.quality.title": "Haute qualité", "features.quality.description": "Sortie avec haute résolution, mouvement fluide et détails précis.", "features.collaboration.title": "Collaboration facile", "features.collaboration.description": "Partagez et collaborez sur des projets avec votre équipe facilement.", "features.export.title": "Exportation multi-format", "features.export.description": "Exporter du contenu dans divers formats adaptés à toutes les plateformes et à tous les usages.", "features.cta.title": "Prêt à commencer ?", "features.cta.description": "Découvrez la puissance de l'IA dans la création de contenu aujourd'hui.", "features.cta.button": "<PERSON><PERSON>ez votre premier contenu", "howto.title": "Comment créer du contenu IA", "howto.subtitle": "Avec seulement 4 étapes simples, vous pouvez créer du contenu professionnel avec l'IA.", "howto.step1.title": "Décrivez votre idée", "howto.step1.description": "Rédigez une description détaillée du contenu que vous souhaitez créer. Incluez des scènes, des personnages, des actions et le style.", "howto.step2.title": "Personnaliser les paramètres", "howto.step2.description": "Choisissez la résolution, le rapport d'aspect, le style et d'autres paramètres adaptés à votre objectif.", "howto.step3.title": "Traitement de l'IA", "howto.step3.description": "L'intelligence artificielle analysera votre demande et créera un contenu de haute qualité en quelques secondes.", "howto.step4.title": "Télécharger et partager", "howto.step4.description": "Téléchargez du contenu sur votre ordinateur ou partagez-le directement sur vos plateformes de réseaux sociaux préférées.", "howto.examples.title": "Exemples de contenu IA", "howto.examples.subtitle": "Voir le contenu créé à partir de simples invites", "howto.cta": "Essayez de créer maintenant", "faq.title": "Questions fréquemment posées", "faq.description": "Trouver des réponses aux questions fréquentes sur la création de contenu par l'IA", "faq.general.title": "Général", "faq.services.title": "Services", "faq.pricing.title": "Tarification", "faq.contact.title": "Vous avez encore des questions ?", "faq.contact.description": "Notre équipe de support est prête à vous aider 24 heures sur 24, 7 jours sur 7.", "faq.contact.email": "Envoyer un courriel", "faq.contact.discord": "Rejoin<PERSON>", "footer.product.title": "Produit", "footer.product.features": "Caractéristiques", "footer.product.howto": "Comment utiliser", "footer.product.pricing": "Tarification", "footer.product.app": "Application", "footer.company.title": "Entreprise", "footer.company.about": "À propos de nous", "footer.company.blog": "Blog", "footer.company.careers": "Carrières", "footer.company.contact": "Contact", "footer.legal.title": "Légal", "footer.legal.privacy": "Politique de confidentialité", "footer.legal.terms": "Conditions d'utilisation", "footer.legal.cookies": "Politique en matière de cookies", "footer.support.title": "<PERSON><PERSON><PERSON>", "footer.support.help": "Centre d'aide", "footer.support.api": "Docs de l'API", "footer.support.status": "État du système", "footer.description": "Créez du contenu IA de haute qualité avec la technologie la plus avancée. Transformez des idées en contenu professionnel en quelques secondes.", "footer.newsletter.title": "Obt<PERSON>z les dernières mises à jour", "footer.newsletter.description": "Abonnez-vous pour recevoir des nouvelles sur les nouvelles fonctionnalités et les mises à jour des produits.", "footer.newsletter.placeholder": "Entrez votre adresse e-mail", "footer.newsletter.subscribe": "<PERSON>'abonner", "footer.copyright": "© 2024 GeminiGen AI. Tous droits réservés.", "footer.language": "<PERSON><PERSON><PERSON>", "Features That Set Us Apart": "Caractéristiques qui nous distinguent", "howto.examples.tryPrompt": "Essayez cette invite", "howto.examples.example1.title": "<PERSON><PERSON><PERSON>", "howto.examples.example1.prompt": "Un chat courant à travers un champ de fleurs de cerisier au coucher du soleil.", "howto.examples.example2.title": "Surf sur l'océan", "howto.examples.example2.prompt": "Un homme surfant sur les vagues bleues de l'océan par une belle journée ensoleillée.", "howto.examples.example3.title": "Ville du Futur", "howto.examples.example3.prompt": "Une ville futuriste avec des voitures volantes et des gratte-ciels imposants", "howto.examples.example4.title": "Danse culturelle", "howto.examples.example4.prompt": "Danseurs traditionnels se produisant en costumes colorés lors d'un festival.", "howto.examples.example5.title": "Paysage de montagne", "howto.examples.example5.prompt": "Un paysage de montagne serein avec des rivières sinueuses et des sommets embrumés", "videoGen.imageToVideo": "Image-to-Video", "videoGen.textToVideo": "Texte en vidéo", "videoGen.generated": "<PERSON><PERSON><PERSON><PERSON>", "videoGen.imageToVideoGenerated": "Image générée en vidéo", "videoGen.textToVideoGenerated": "Texte-à-vid<PERSON><PERSON>", "videoGen.generationStarted": "Nous commençons à générer votre vidéo, veuil<PERSON>z patienter environ 2 minutes...", "videoGen.imageToVideoGenerationStarted": "Nous commençons à générer votre vidéo à partir de l'image, veuillez patienter environ 2 minutes...", "videoGen.textToVideoGenerationStarted": "Nous commençons à générer votre vidéo à partir du texte, veuillez patienter environ 2 minutes...", "videoGen.pleaseEnterPrompt": "<PERSON><PERSON><PERSON><PERSON> entrer une invite pour générer une vidéo.", "videoGen.cinematic": "Cinematographique", "videoGen.model": "<PERSON><PERSON><PERSON><PERSON>", "videoGen.imageReference": "Référence d'image", "videoGen.prompt": "Invite", "videoGen.negativePrompt": "Invite négative", "videoGen.negativePromptDescription": "Décrivez ce que vous ne voulez pas voir dans la vidéo.", "videoGen.negativePromptPlaceholder": "Entrez ce que vous voulez exclure de la vidéo...", "videoGen.negativePromptTooltip": "Les invites négatives aident à exclure les éléments, styles ou concepts indésirables de votre génération vidéo. Par exemple : « flou, basse qualité, visages déformés »", "videoGen.negativePromptSuggestions": "Suggestions rapides", "videoGen.negativePromptSuggestion1": "flou, de faible qualité", "videoGen.negativePromptSuggestion2": "visages déformés", "videoGen.negativePromptSuggestion3": "Ana<PERSON><PERSON> d<PERSON>", "videoGen.negativePromptSuggestion4": "filigrane, texte", "videoGen.negativePromptSuggestion5": "sursaturé", "videoGen.negativePromptSuggestion6": "mouvement irréaliste", "videoGen.enhancePrompt": "Améliorer l'invite", "videoGen.enhancePromptOn": "<PERSON> invites seront toujours affinées pour améliorer la qualité des résultats.", "videoGen.enhancePromptOnRequired": "Les invites seront toujours affinées pour améliorer la qualité des résultats (nécessaire pour ce modèle).", "videoGen.enhancePromptOff": "Les invites ne seront pas modifiées", "videoGen.enhancePromptLocked": "Ce paramètre est verrouillé pour le modèle sélectionné.", "videoGen.enhancePromptNotRefined": "Les invites ne seront pas affinées", "videoGen.aspectRatio": "Format d'image", "videoGen.duration": "<PERSON><PERSON><PERSON>", "videoGen.selectDuration": "Sélectionner la durée de la vidéo en secondes", "videoGen.creditsRemaining": "Crédits : {credits} restants", "videoGen.generationCost": "Cette génération coûtera : {cost} crédits pour {duration}s", "videoGen.generateVideo": "Générer une vidéo", "videoGen.somethingWentWrong": "<PERSON><PERSON><PERSON> chose a mal tourné.", "videoGen.examplesTitle": "Exemples de génération vidéo par IA", "videoGen.examplesDescription": "Explorez la puissance de la génération vidéo par IA avec ces comparaisons interactives.", "videoGen.processing": "Traitement de votre vidéo...", "videoGen.analyzing": "Analyse de votre saisie...", "videoGen.rendering": "Rendu de votre vidéo...", "videoGen.finalizing": "Finalisation de votre vidéo...", "videoGen.almostReady": "Votre vidéo est presque prête...", "videoGen.estimatedTime": "Temps estimé : 2-3 minutes", "videoGen.backgroundProcessing": "Votre vidéo est en cours de génération en arrière-plan.", "videoGen.checkHistory": "Vous pouvez vérifier les progrès dans votre historique.", "videoGen.willNotify": "Nous vous informerons lorsque ce sera prêt.", "videoGen.error.invalidPrompt": "<PERSON><PERSON><PERSON><PERSON> entrer une invite valide.", "videoGen.error.invalidModel": "Veuillez sélectionner un modèle valide.", "videoGen.error.invalidDuration": "Veuillez sélectionner une durée valide.", "videoGen.error.invalidAspectRatio": "Veuillez sélectionner un format d'image valide.", "videoGen.error.insufficientCredits": "Crédits insuffisants pour la génération de vidéos.", "videoGen.error.fileTooLarge": "Le fichier image sélectionné est trop volumineux.", "videoGen.error.invalidFileFormat": "Format de fichier image invalide", "videoGen.error.generationFailed": "Échec de la génération de la vidéo", "videoGen.error.networkError": "<PERSON><PERSON><PERSON> r<PERSON>eau lors de la génération de la vidéo.", "videoGen.error.serverError": "Erreur du serveur lors de la génération de la vidéo", "videoGen.success.generationStarted": "La génération de vidéo a démarré avec succès.", "videoGen.success.generationCompleted": "Génération de vidéo terminée avec succès.", "videoGen.success.imageUploaded": "Image téléchargée avec succès", "videoGen.success.promptSaved": "Invite enregis<PERSON><PERSON> avec succès", "Image Generation": "Génération d'images", "We are starting to generate your image, please wait about 1 minute...": "Nous commençons à générer votre image, veuil<PERSON>z patienter environ 1 minute...", "We are starting to generate speech from your document, please check back later...": "Nous commençons à générer la voix à partir de votre document, veuillez revenir plus tard...", "We are starting to generate your speech, please wait about {time} {unit}...": "Nous commençons à générer votre discours, veuillez patienter environ {time} {unit}...", "minutes": "minutes", "seconds": "secondes", "faq.questions.q1.title": "Pourquoi choisir geminigen.ai plutôt que d'autres outils TTV ?", "faq.questions.q1.content": "Geminigen.ai propose des images et des vidéos générées par IA à partir de texte à un prix plus abordable par rapport aux autres applications sur le marché. En plus de la génération d'images et de vidéos à partir de texte, nous fournissons également des services de synthèse vocale et de génération de conversations basées sur le texte.", "faq.questions.q2.title": "Comment puis-je utiliser le service geminigen.ai ?", "faq.questions.q2.content": "Notre service est conçu pour être convivial. Il vous suffit de décrire en texte la vidéo que vous souhaitez, et le système la convertira automatiquement en vidéo.", "faq.questions.q3.title": "Ai-je besoin de connaissances en programmation pour utiliser geminigen.ai ?", "faq.questions.q3.content": "Non, vous n'avez pas besoin de connaissances en programmation. Nous avons intégré l'API TTV de Gemini dans notre site web, rendant le processus de conversion de texte en images et vidéos simple et pratique pour tout le monde.", "faq.questions.q4.title": "Quelles langues sont prises en charge pour la saisie de texte ?", "faq.questions.q4.content": "Nous prenons actuellement en charge plusieurs langues, dont l'anglais, le vietnamien, l'espagnol, et bien d'autres. Le support pour des langues supplémentaires est ajouté régulièrement.", "faq.questions.q5.title": "Quel est le coût d'utilisation de geminigen.ai ?", "faq.questions.q5.content": "Nous fixons nos prix en fonction de ceux de Gemini, garantissant des coûts inférieurs à ceux de nombreux autres outils de conversion de texte en parole sur le marché. Vous pouvez consulter les prix détaillés sur la page : https://geminigen.ai/pricing", "faq.questions.q6.title": "Puis-je utiliser geminigen.ai à des fins commerciales ?", "faq.questions.q6.content": "<PERSON><PERSON>, notre service prend en charge à la fois les usages personnels et commerciaux. <PERSON><PERSON>dant, veuillez vous assurer de respecter nos conditions d'utilisation.", "faq.questions.q7.title": "Quelle est la qualité de la vidéo sur geminigen.ai ?", "faq.questions.q7.content": "Les images et vidéos générées sont de haute qualité, avec des visuels réalistes et vivants grâce à la technologie avancée TTV de Gemini.", "faq.questions.q8.title": "Comment puis-je protéger ma vie privée et mes données sur geminigen.ai ?", "faq.questions.q8.content": "La sécurité et la confidentialité des utilisateurs sont nos principales priorités. Nous employons des mesures de sécurité avancées pour protéger vos données et ne partageons pas d'informations avec des tiers sans votre consentement.", "faq.questions.q9.title": "Pour la conversion de texte en parole, puis-je modifier ou personnaliser le fichier audio de sortie ?", "faq.questions.q9.content": "Bien que nous ne fournissions pas de fonctions d'édition directe sur la plateforme, vous pouvez personnaliser certains paramètres tels que la vitesse de lecture et le ton avant la conversion. Cela vous permet d'avoir un meilleur contrôle sur le ressenti et le son final du fichier de sortie.", "faq.questions.q10.title": "Pour la conversion de texte en parole, puis-je demander des voix supplémentaires ou de nouvelles langues ?", "faq.questions.q10.content": "Nous écoutons toujours les commentaires des utilisateurs et nous efforçons d'élargir nos services. Si vous avez des demandes spécifiques pour une voix ou une langue particulière, n'hésitez pas à les soumettre à notre système de support.", "faq.questions.q11.title": "Où puis-je contacter le support technique si je rencontre des problèmes en utilisant le service ?", "faq.questions.q11.content": "Nous offrons un support par e-mail et chat en direct sur le site web. Notre équipe de support est toujours prête à répondre à toutes vos questions et à vous aider lorsque cela est nécessaire.", "faq.questions.q12.title": "Ai-je besoin de créer un compte pour utiliser le service ?", "faq.questions.q12.content": "<PERSON><PERSON>, créer un compte vous aide à gérer facilement les images, vidéos et documents convertis et à accéder à des fonctionnalités avancées ainsi qu'à de meilleurs services d'assistance client.", "faq.questions.q13.title": "Puis-je utiliser geminigen.ai pour créer du contenu pour mon site web ou blog ?", "faq.questions.q13.content": "<PERSON><PERSON>, vous pouvez utiliser notre service pour créer du contenu audio pour votre site web, blog ou plateformes de médias sociaux, enrichissant ainsi la manière dont vous diffusez l'information à vos lecteurs ou clients.", "faq.questions.q14.title": "Quel est le format de fichier de sortie de l'image ?", "faq.questions.q14.content": "Le format de fichier de sortie principal est png, ce qui garantit la compatibilité avec la plupart des appareils.", "faq.questions.q15.title": "Combien cela coûte-t-il de générer une vidéo ?", "faq.questions.q15.content": "Nous calculons les crédits en fonction du nombre de secondes de la vidéo que vous créez. Vous pouvez consulter les prix détaillés sur la page : https://geminigen.ai/pricing", "faq.questions.q16.title": "Quel est le format de fichier de sortie du discours ?", "faq.questions.q16.content": "Le format de fichier de sortie principal est MP3, WAV, ce qui garantit la compatibilité avec la plupart des appareils et des logiciels de lecture de musique.", "faq.questions.q17.title": "Le processus de paiement est-il difficile ? Quels sont les modes de paiement disponibles ?", "faq.questions.q17.content": "Le processus de paiement est très simple. Nous proposons des méthodes de paiement populaires, y compris PayPal, carte de débit et cryptomonnaie.", "faq.questions.q18.title": "Existe-t-il des abonnements pour les utilisateurs fréquents ?", "faq.questions.q18.content": "Non, nous vendons des crédits sans date d'expiration. Lorsque vous n'avez plus de crédits, achetez un autre pack de crédits.", "faq.questions.q19.title": "Vais-je obtenir un remboursement si ma vidéo <PERSON>ue ou présente une erreur ?", "faq.questions.q19.content": "Nous comptabiliserons les crédits lorsque votre vidéo sera créée avec succès. En cas d'erreurs, les crédits ne seront pas comptabilisés.", "Transform your ideas into stunning AI-generated images, videos, speech, and more.": "Transformez vos idées en superbes images, vidéos, discours et plus encore générés par l'IA.", "Save up to {0} compared to traditional creative services while experiencing the future of content generation.": "Économisez jusqu'à {0} par rapport aux services créatifs traditionnels tout en découvrant l'avenir de la génération de contenu.", "30 seconds": "30 secondes", "5 minutes": "5 minutes", "Minimum required": "Minimum requis", "Audio duration must be at least {duration}": "La durée audio doit être d'au moins {duration}", "The sound should be clear, without any noise, and last at least 5 minutes to ensure good quality.": "Le son doit être clair, sans aucun bruit, et durer au moins 5 minutes pour garantir une bonne qualité.", "The sound should be clear, without any noise, and last at least 30 seconds to ensure good quality.": "Le son doit être clair, sans aucun bruit, et durer au moins 30 secondes pour garantir une bonne qualité.", "videoGen.resolution": "Résolution", "videoGen.selectResolution": "Sélectionner la qualité de résolution vidéo", "GEMINI_RATE_LIMIT": "Le serveur connaît actuellement un trafic élevé. Veuillez réessayer plus tard.", "Create stunning images from text prompts using advanced AI models": "Créer des images époustouflantes à partir de descriptions textuelles en utilisant des modèles d'IA avancés.", "AI Video Generator": "Générateur de vidéos IA", "Create professional videos from text prompts using advanced AI models": "Créer des vidéos professionnelles à partir de commandes textuelles en utilisant des modèles d'IA avancés.", "AI Speech Generator": "Générateur de discours IA", "Convert text to natural-sounding speech with 400+ AI voices": "Convertissez le texte en un discours naturel avec plus de 400 voix d'IA.", "AI Dialogue Generator": "Générateur de dialogue IA", "Create realistic conversations between multiple AI speakers": "Créer des conversations réalistes entre plusieurs interlocuteurs d'IA", "Video Generation Pricing by Resolution": "Tarification de la génération de vidéos par résolution", "Different video resolutions have different pricing. Higher resolutions cost more but provide better quality.": "Les différentes résolutions vidéo ont des tarifs différents. Les résolutions plus élevées coûtent plus cher mais offrent une meilleure qualité.", "Base Price": "Prix de base", "HD Resolution": "Résolution HD", "Full HD Resolution": "Résolution Full HD", "HD Resolution Only": "Résolution HD uniquement", "Full HD not available for Veo 2": "Full HD non disponible pour Veo 2", "off": "<PERSON><PERSON><PERSON>", "Veo 2 HD": "Veo 2 HD", "Veo 3 Fast HD (With Audio)": "Veo 3 Rapide HD (Avec Audio)", "Veo 3 Fast Full HD (With Audio)": "Veo 3 Rapide Full HD (Avec Audio)", "Veo 3 HD": "Je vois 3 HD", "Veo 3 Full HD": "Je vois 3 Full HD", "Server": "Ser<PERSON><PERSON>", "Regular Server": "<PERSON><PERSON><PERSON>", "VIP Server": "Serveur VIP", "Reliable but may be slower at peak times": "<PERSON>able mais peut être plus lent aux heures de pointe.", "Smooth, priority performance, no delays": "Performances fluides et prioritaires, sans retard.", "Promotional Price": "Prix promotionnel", "NOT_ENOUGH_AND_LOCK_CREDIT": "Votre compte n'a pas suffisamment de crédit. Veuillez recharger votre compte pour continuer.", "videoGen.creditsNeeded": "Crédits nécessaires", "videoGen.creditsAvailable": "Crédits disponibles", "videoGen.creditsShortfall": "Déficit de crédits", "NOT_ENOUGH_CREDIT_TITLE": "Crédits insuffisants", "NOT_ENOUGH_CREDIT_MESSAGE": "Vous avez besoin de plus de crédits pour générer cette vidéo. Rechargez votre compte pour continuer à créer du contenu incroyable.", "BUY_CREDITS_NOW": "Achetez des crédits maintenant", "VIEW_PRICING": "Voir les tarifs", "videoGen.willPurchase": "Achat minimum de 10 $", "NOT_ENOUGH_CREDIT_MESSAGE_VIDEO": "Vous avez besoin de plus de crédits pour générer cette vidéo. Rechargez votre compte pour continuer à créer du contenu incroyable.", "NOT_ENOUGH_CREDIT_MESSAGE_SPEECH": "Vous avez besoin de plus de crédits pour générer ce discours. Rechargez votre compte pour continuer à créer un contenu incroyable.", "NOT_ENOUGH_CREDIT_MESSAGE_DIALOG": "Il vous faut plus de crédits pour générer ce dialogue. Recharger votre compte pour continuer à créer du contenu incroyable.", "NOT_ENOUGH_CREDIT_MESSAGE_IMAGE": "Vous avez besoin de plus de crédits pour générer cette image. Rechargez votre compte pour continuer à créer du contenu incroyable.", "creditsNeeded": "Crédits requis", "creditsAvailable": "Crédits disponibles", "creditsShortfall": "Déficit de crédits", "willPurchase": "Achètera", "MINIMUM_PURCHASE_10": "Achat minimum : 10 $", "imageGen.creditsNeeded": "Crédits nécessaires", "imageGen.creditsAvailable": "Crédits disponibles", "imageGen.creditsShortfall": "Déficit de crédits", "imageGen.willPurchase": "Achètera", "NOT_ENOUGH_CREDIT_MESSAGE_VIDEO_C": "Vous avez besoin de plus de crédits pour générer cette vidéo. Seulement ${money}/{credit} crédits, {veo3FastCredit} crédits ~ ${veo3FastPrice}/vidéo Veo 3 Fast, {veo3Credit} crédits ~ ${veo3Price}/vidéo Veo 3, {veo2Credit} crédits ~ ${veo2Price}/vidéo Veo 2. Économisez jusqu'à {save}% par rapport à Replicate & Fal.ai.", "NOT_ENOUGH_CREDIT_MESSAGE_IMAGE_C": "Vous avez besoin de plus de crédits pour générer cette image. Seulement ${money}/{credit} crédits, {imagenFlashCredit} crédits ~ ${imagenFlashPrice}/image Gemini 2.5 Flash, {imagen4FastCredit} crédits ~ ${imagen4FastPrice}/image Imagen 4 Fast, {imagen4Credit} crédits ~ ${imagen4Price}/image Imagen 4, {imagen4UltraCredit} crédits ~ ${imagen4UltraPrice}/image Imagen 4 Ultra. Économisez jusqu'à {save}%+ par rapport à Replicate & Fal.ai.", "NOT_ENOUGH_CREDIT_MESSAGE_SPEECH_C": "Vous avez besoin de plus de crédits pour générer ce discours. Vous avez seulement ${money}/{credit} crédits ~ {characters} caractères ~ {hours} heures d'audio créées. Économisez plus de {save}% par rapport à Elevenlabs.", "You can switch server type to see the price difference.": "Vous pouvez changer le type de serveur pour voir la différence de prix.", "Unstable": "Instable", "Stable": "Stable", "Special Promotion - 100% Bonus Credits!": "Promotion spéciale - Crédits bonus de 100 % !", "Get double credits with every purchase. Limited time offer!": "Obtenez le double de crédits pour chaque achat. Offre limitée dans le temps !", "100% Bonus Credits!": "Crédits Bonus de 100 %!", "100% Bonus Credits Active!": "100% de crédits bonus actifs !", "Base: {base} + Bonus: {bonus}": "Base : {base} + Bonus : {bonus}", "You'll receive: {total} credits ({base} base + {bonus} bonus)": "Vous recevrez : {total} crédits ({base} de base + {bonus} de bonus)", "🎉 100% Bonus Credits Active! Get double credits with every purchase!": "🎉 100 % de crédits bonus actifs ! Obtenez des crédits doublés à chaque achat !", "The prompt must be in English. Please try again with a different prompt.": "Le message doit être en anglais. Veuillez réessayer avec un autre message.", "Each amount shows what you can generate with your entire budget (choose one type). Video generation is priced per video, speech and dialogue generation in characters.": "Chaque montant indique ce que vous pouvez générer avec l'ensemble de votre budget (choisissez un type). La génération de vidéos est facturée par vidéo, la génération de discours et de dialogues en caractères.", "videoGen.generationCostPerVideo": "Cette génération coûtera : {cost} Crédits par vidéo", "videoGen.fixedPricePerVideo": "Prix fixe par vidéo", "videoGen.pricingWithResolution": "Prix de base pour 720p, majoration de 50 % pour Full HD", "Video Resolution Settings": "Paramètres de résolution vidéo", "Choose resolution for video generation pricing": "Choisissez la résolution pour la tarification de la génération vidéo.", "Enter number of videos": "Entrez le nombre de vidéos", "per video": "par vidéo", "imageSelect.supportedFormats": "Formats pris en charge", "It will cost {cost} credits each time you create a voice.": "<PERSON><PERSON> {cost} crédits chaque fois que vous créez une voix.", "videoGen.hotPromo": "🔥 Chaud : <PERSON><PERSON>ez des vidéos de 8 secondes avec Veo 3 ! Seulement 0,1 $ pour Rapide et 0,5 $ pour Qualité", "videoGen.videoExtend": "<PERSON><PERSON><PERSON> la vidéo", "videoGen.extendVideo": "<PERSON><PERSON><PERSON> la vidéo", "videoGen.extend": "<PERSON><PERSON><PERSON>", "videoGen.extendingVideo": "Extension de la vidéo", "videoGen.extendPromptPlaceholder": "Décrivez comment vous souhaitez prolonger cette vidéo...", "videoGen.videoExtendGenerationStarted": "Nous commençons à étendre votre vidéo, ve<PERSON><PERSON><PERSON> patienter environ 2 minutes...", "videoGen.selectVideoToExtend": "Sélectionner la Dernière Image de l'Historique Vidéo", "videoGen.searchVideos": "Rechercher des vidéos...", "videoGen.noVideosFound": "<PERSON><PERSON>ne vidéo trouvée", "videoGen.selectVideo": "Sélectionner la vidéo", "Loading videos...": "Chargement des vidéos...", "Model": "<PERSON><PERSON><PERSON><PERSON>", "Created": "<PERSON><PERSON><PERSON>", "videoGen.createNew": "Créer nouveau", "videoGen.extendModeNote": "Mode d'extension vidéo", "videoGen.extendModeDescription": "Les paramètres de Modèle, de Format d'image et de Résolution seront automatiquement définis en fonction de la vidéo sélectionnée et ne peuvent pas être modifiés.", "videoGen.pleaseSelectVideoToExtend": "V<PERSON>illez sélectionner une vidéo à prolonger.", "videoGen.originalVideo": "Vidéo originale", "videoGen.willExtendTo": "Va s'étendre à", "videoGen.extendDescription": "<PERSON><PERSON>er une continuation", "videoGen.extendedVideo": "<PERSON>id<PERSON><PERSON>", "videoGen.newVideo": "Nouvelle vidéo", "videoGen.willBeGenerated": "<PERSON><PERSON>", "videoGen.basedOnPrompt": "Basé sur votre demande", "Select Video from History": "Sélectionner la vidéo de l'historique", "Open Video History": "Ouvrir l'historique vidéo", "imageSelect.importFromHistory": "Importer depuis l'historique", "imageSelect.selectFromHistory": "Sélectionner dans l'historique", "imageSelect.noImagesInHistory": "Aucune image trouvée dans l'historique.", "imageSelect.loadingImages": "Chargement des images...", "imageSelect.selectImage": "Sélectionner l'image", "imageSelect.selectedFromHistory": "Sélectionné dans l'histoire", "imageSelect.scrollToSeeMore": "Faites défiler pour voir plus d'images", "upload": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "videoGen.extendGuideTitle": "Générer le prochain segment vidéo", "videoGen.extendGuideDescription": "G<PERSON><PERSON>rez la prochaine vidéo en décrivant dans l'invite ce qui se passe à partir de l'image sélectionnée. Soyez précis quant aux actions, mouvements ou changements que vous voulez voir dans la suite.", "Last Frame": "Dernière Image"}