#!/usr/bin/env node

/**
 * <PERSON>ript to generate static sitemap files for static deployment
 * Run with: node scripts/generate-static-sitemaps.js
 */

const fs = require('fs')
const path = require('path')

const baseUrl = 'https://geminigen.ai'
const currentDate = new Date().toISOString().split('T')[0]

// Ensure public directory exists
const publicDir = path.join(__dirname, '..', 'public')
if (!fs.existsSync(publicDir)) {
  fs.mkdirSync(publicDir, { recursive: true })
}

// Main sitemap routes
const mainRoutes = [
  // Main pages - highest priority
  {
    url: '/',
    lastmod: currentDate,
    changefreq: 'daily',
    priority: '1.0'
  },
  {
    url: '/app',
    lastmod: currentDate,
    changefreq: 'weekly',
    priority: '0.9'
  },

  // AI Tools - high priority for main features
  {
    url: '/app/video-gen',
    lastmod: currentDate,
    changefreq: 'weekly',
    priority: '0.9'
  },
  {
    url: '/app/speech-gen',
    lastmod: currentDate,
    changefreq: 'weekly',
    priority: '0.9'
  },
  {
    url: '/app/dialogue-gen',
    lastmod: currentDate,
    changefreq: 'weekly',
    priority: '0.9'
  },
  {
    url: '/app/imagen',
    lastmod: currentDate,
    changefreq: 'weekly',
    priority: '0.9'
  },
  {
    url: '/app/music-gen',
    lastmod: currentDate,
    changefreq: 'weekly',
    priority: '0.9'
  },

  // Important business pages
  {
    url: '/pricing',
    lastmod: currentDate,
    changefreq: 'monthly',
    priority: '0.8'
  },

  // Public pages
  {
    url: '/public',
    lastmod: currentDate,
    changefreq: 'monthly',
    priority: '0.7'
  },
  {
    url: '/public/thank-you',
    lastmod: currentDate,
    changefreq: 'monthly',
    priority: '0.5'
  },

  // Authentication pages - important for user acquisition
  {
    url: '/auth/login',
    lastmod: currentDate,
    changefreq: 'monthly',
    priority: '0.6'
  },
  {
    url: '/auth/signup',
    lastmod: currentDate,
    changefreq: 'monthly',
    priority: '0.6'
  },
  {
    url: '/auth/account-recovery',
    lastmod: currentDate,
    changefreq: 'monthly',
    priority: '0.4'
  },
  {
    url: '/auth/reset-password',
    lastmod: currentDate,
    changefreq: 'monthly',
    priority: '0.4'
  },

  // Legal pages - required but lower priority
  {
    url: '/terms',
    lastmod: currentDate,
    changefreq: 'yearly',
    priority: '0.4'
  },
  {
    url: '/privacy',
    lastmod: currentDate,
    changefreq: 'yearly',
    priority: '0.4'
  }
]

// Documentation routes
const docRoutes = [
  {
    url: '/docs',
    lastmod: currentDate,
    changefreq: 'weekly',
    priority: '0.7'
  },
  {
    url: '/docs/getting-started',
    lastmod: currentDate,
    changefreq: 'weekly',
    priority: '0.8'
  },
  {
    url: '/docs/essentials',
    lastmod: currentDate,
    changefreq: 'weekly',
    priority: '0.7'
  },
  {
    url: '/docs/examples',
    lastmod: currentDate,
    changefreq: 'weekly',
    priority: '0.6'
  },
  {
    url: '/docs/guides',
    lastmod: currentDate,
    changefreq: 'weekly',
    priority: '0.6'
  }
]

// Blog routes
const blogRoutes = [
  {
    url: '/blog',
    lastmod: currentDate,
    changefreq: 'weekly',
    priority: '0.8'
  }
  // Add individual blog posts here when available
]

function generateSitemap(routes) {
  return `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.sitemaps.org/schemas/sitemap/0.9
        http://www.sitemaps.org/schemas/sitemap/0.9/sitemap.xsd">
${routes.map(route => `  <url>
    <loc>${baseUrl}${route.url}</loc>
    <lastmod>${route.lastmod}</lastmod>
    <changefreq>${route.changefreq}</changefreq>
    <priority>${route.priority}</priority>
  </url>`).join('\n')}
</urlset>`
}

function generateSitemapIndex() {
  const currentDateTime = new Date().toISOString()
  
  const sitemaps = [
    {
      url: `${baseUrl}/sitemap.xml`,
      lastmod: currentDateTime
    },
    {
      url: `${baseUrl}/sitemap-docs.xml`,
      lastmod: currentDateTime
    },
    {
      url: `${baseUrl}/sitemap-blog.xml`,
      lastmod: currentDateTime
    }
  ]

  return `<?xml version="1.0" encoding="UTF-8"?>
<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${sitemaps.map(sitemap => `  <sitemap>
    <loc>${sitemap.url}</loc>
    <lastmod>${sitemap.lastmod}</lastmod>
  </sitemap>`).join('\n')}
</sitemapindex>`
}

// Generate all sitemap files
console.log('🗺️  Generating static sitemap files...')

// Main sitemap
const mainSitemap = generateSitemap(mainRoutes)
fs.writeFileSync(path.join(publicDir, 'sitemap.xml'), mainSitemap)
console.log('✅ Generated sitemap.xml')

// Documentation sitemap
const docsSitemap = generateSitemap(docRoutes)
fs.writeFileSync(path.join(publicDir, 'sitemap-docs.xml'), docsSitemap)
console.log('✅ Generated sitemap-docs.xml')

// Blog sitemap
const blogSitemap = generateSitemap(blogRoutes)
fs.writeFileSync(path.join(publicDir, 'sitemap-blog.xml'), blogSitemap)
console.log('✅ Generated sitemap-blog.xml')

// Sitemap index
const sitemapIndex = generateSitemapIndex()
fs.writeFileSync(path.join(publicDir, 'sitemap-index.xml'), sitemapIndex)
console.log('✅ Generated sitemap-index.xml')

console.log('\n🎉 All sitemap files generated successfully!')
console.log(`📅 Generated on: ${currentDate}`)
console.log(`📍 Files location: ${publicDir}`)
