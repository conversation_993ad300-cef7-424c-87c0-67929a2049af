{"name": "imagen-frontend", "private": true, "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "node scripts/generate-static-sitemaps.js && nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "lint": "eslint .", "lint:fix": "eslint . --fix", "typecheck": "nuxt typecheck", "translate-locales": "./node_modules/.bin/translate-locales", "i18n:check": "node scripts/check-missing-translations.js", "i18n:fix": "node scripts/fix-missing-translations.js", "seo:test": "node scripts/test-sitemap.js", "seo:test:local": "node scripts/test-sitemap-local.js", "seo:submit": "node scripts/submit-sitemap.js", "seo:generate": "node scripts/generate-static-sitemaps.js", "i18n:add-missing": "node scripts/add-missing-keys.js", "i18n:validate": "node scripts/validate-i18n.js"}, "dependencies": {"@iconify-json/lucide": "^1.2.52", "@iconify-json/simple-icons": "^1.2.40", "@nuxt/content": "^3.6.1", "@nuxt/image": "^1.10.0", "@nuxt/ui-pro": "^3.2.0", "@nuxtjs/supabase": "1.5.1", "@paypal/paypal-js": "^8.2.0", "@pinia/nuxt": "^0.11.1", "@supabase/supabase-js": "^2.50.2", "@vueuse/nuxt": "^13.4.0", "animate.css": "^4.1.1", "animejs": "^4.0.2", "axios": "^1.12.2", "better-sqlite3": "^12.1.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "driver.js": "^1.3.6", "motion-v": "^1.3.1", "nuxt": "^3.17.5", "nuxt-og-image": "^5.1.8", "nuxt-vue3-google-signin": "^0.0.11", "pinia": "^3.0.3", "pinia-plugin-persistedstate": "^4.3.0", "simplex-noise": "^4.0.3", "three": "^0.176.0", "translate-locales": "^1.1.2", "vue-audio-visual": "^3.0.11", "vue-number-animation": "2.0.2", "vue3-google-signin": "^2.1.1", "wavesurfer.js": "^7.9.8"}, "devDependencies": {"@nuxt/eslint": "^1.4.1", "@nuxtjs/i18n": "^9.5.6", "@types/crypto-js": "^4.2.2", "eslint": "^9.29.0", "sass-embedded": "^1.89.2", "typescript": "^5.8.3", "vue-tsc": "^2.2.10"}, "resolutions": {"unimport": "4.1.1"}, "pnpm": {"onlyBuiltDependencies": ["better-sqlite3", "sharp"], "ignoredBuiltDependencies": ["@parcel/watcher", "esbuild", "vue-demi"]}, "packageManager": "pnpm@10.10.0+sha512.d615db246fe70f25dcfea6d8d73dee782ce23e2245e3c4f6f888249fb568149318637dca73c2c5c8ef2a4ca0d5657fb9567188bfab47f566d1ee6ce987815c39"}