import { ref, computed } from 'vue'

export const useImageHistory = () => {
  const historyStore = useHistoryStore()
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  const histories = ref<any[]>([])
  // Computed để lọc chỉ những history có type là 'image'
  const imageHistories = computed(() => {
    console.log(
      '🚀 ~ useImageHistory ~ historyStore.histories:',
      historyStore.histories
    )
    return histories.value.filter(
      history =>
        history.type === 'image'
        && history.status === 2 // Status 2 là completed
        && (history as any).thumbnail_url
    )
  })

  // Function để fetch image histories
  const fetchImageHistories = async (limit = 20) => {
    try {
      isLoading.value = true
      error.value = null
      const queryParams = new URLSearchParams()

      queryParams.append('filter_by', 'image')
      queryParams.append('has_last_frame', 'false')
      queryParams.append('items_per_page', String(limit || 100))
      const { apiService } = useAPI()
      const response = await apiService.get(
        `/histories?${queryParams.toString()}`
      )
      histories.value = response.data?.result
    } catch (err: any) {
      error.value = err.message || 'Failed to load image history'
      console.error('🚀 ~ fetchImageHistories error:', err)
    } finally {
      isLoading.value = false
    }
  }

  // Function để convert history image thành ImageFile format
  const convertHistoryToImageFile = (
    history: any
  ): { src: string, alt: string, file?: File, ref_history: string } => {
    return {
      src: history.thumbnail_url,
      alt: history.input_text || `Generated image ${history.id}`,
      // Không có file thật, chỉ có URL
      file: undefined,
      ref_history: history.uuid
    }
  }

  return {
    imageHistories,
    isLoading,
    error,
    fetchImageHistories,
    convertHistoryToImageFile
  }
}
